package com.polarizon.gendo.sg.push.message.api;


import com.fasterxml.jackson.annotation.JsonView;
import com.polarizon.common.bean.dto.ResultDTO;
import com.polarizon.common.utils.Constants;
import com.polarizon.gendo.sg.authority.bean.UserEntity;
import com.polarizon.gendo.sg.authority.bean.dto.LoginResultDTO;
import com.polarizon.gendo.sg.authority.bean.form.UserAuthFormBean;
import com.polarizon.gendo.sg.push.message.bean.bo.AccessTokenBO;
import com.polarizon.gendo.sg.push.message.bean.bo.DdConfigSignBO;
import com.polarizon.gendo.sg.push.message.bean.bo.EventMessageBO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletRequest;

@Api(tags = {"钉钉对接控制器"})
@FeignClient(url = "${com.polarizon.feign.push-message-api-url:http://push-message:20208}/push-message/v1/dingtalk",
    name = "DingtalkControllerInterface")
public interface DingtalkControllerInterface {
    @ApiOperation("钉钉授权，获取AccessToken")
    @GetMapping("/accessToken")
    ResultDTO<AccessTokenBO> getAccessToken();

    @ApiOperation("获取钉钉签名")
    @GetMapping("/sign")
    ResultDTO<DdConfigSignBO> getDdConfigSign(
        @ApiParam("需要签名的url，非必传，不传默认签名当前请求url")
        @RequestParam(name = "url", required = false) String url, HttpServletRequest request);

    @ApiOperation("钉钉端登录，登录成功后会将userid绑定当前用户，同一个用户只允许绑定一个userid")
    @PostMapping("/login")
    ResultDTO<LoginResultDTO> login(@RequestBody @JsonView(UserAuthFormBean.WechatLoginView.class) UserAuthFormBean userAuthFormBean);

    @ApiOperation("钉钉解绑")
    @PostMapping("/unbind")
    ResultDTO<UserEntity> unbind(@ApiParam("用户id") @RequestHeader(name = Constants.HEADER_ACCOUNT_KEY) String id);

    @ApiOperation("事件消息推送")
    @PostMapping("/push/message")
    ResultDTO<Object> push(@ApiParam("租户id") @RequestHeader(name = Constants.HEADER_TENANT_ID_KEY) String tenantId,
                           @ApiParam("用户id") @RequestHeader(name = Constants.HEADER_ACCOUNT_KEY) String userId,
                           @RequestBody @JsonView(EventMessageBO.DingtalkMessageView.class) @Validated EventMessageBO eventMessageBO);
}
