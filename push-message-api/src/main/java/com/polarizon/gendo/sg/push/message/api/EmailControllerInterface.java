package com.polarizon.gendo.sg.push.message.api;

import com.polarizon.common.bean.dto.ResultDTO;
import com.polarizon.gendo.sg.push.message.bean.bo.MailMessageBO;
import com.polarizon.gendo.sg.push.message.bean.bo.MailVerificationBO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 邮箱对接api接口
 */
@Api(tags = {"邮箱对接控制器"})
@FeignClient(url = "${com.polarizon.feign.push-message-api-url:http://push-message:20208}/push-message/v1/email",
    name = "EmailControllerInterface")
public interface EmailControllerInterface {

    /**
     * 发送验证码
     *
     * @param messageBO 邮件发送验证码
     * @return
     */
    @ApiOperation("发送验证码")
    @PostMapping("/verify/send")
    ResultDTO<Boolean> sendVerificationCode(@RequestBody @Validated MailVerificationBO messageBO);

    /**
     * 校验验证码
     *
     * @return
     */
    @ApiOperation("校验验证码")
    @PostMapping("/verify/valid")
    ResultDTO<Boolean> validVerificationCode(@RequestParam @ApiParam("邮箱") String key
        , @RequestParam @ApiParam("验证码") String verifyCode);

    /**
     * 批量发送
     *
     * @return
     */
    @ApiOperation("批量发送")
    @PostMapping("/send/batch")
    ResultDTO<Boolean> sendBatch(@RequestBody @Validated MailMessageBO messageBO);

    /**
     * 发送给组织管理员
     *
     * @return
     */
    @ApiOperation("发给组织管理员")
    @PostMapping("/send/admin")
    ResultDTO<Boolean> sendToAdmin(@RequestParam @ApiParam("主题") String subject,
                                   @RequestParam @ApiParam("正文内容") String text,
                                   @RequestParam @ApiParam("邮箱地址") List<String> emails);

}
