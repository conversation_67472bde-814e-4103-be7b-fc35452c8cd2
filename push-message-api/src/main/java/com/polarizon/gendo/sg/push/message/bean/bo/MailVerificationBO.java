package com.polarizon.gendo.sg.push.message.bean.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * 邮件验证码对象
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MailVerificationBO {
    @NotBlank(message = "收件人不能为空")
    @ApiModelProperty("收件人")
    private String toAddress;

    @NotBlank(message = "主题不能为空")
    @ApiModelProperty("主题")
    private String subject;

    @ApiModelProperty("验证码长度 默认6")
    private Integer codeLength;

    @ApiModelProperty("验证码是否包含字母")
    private Boolean codeBeChar;
}
