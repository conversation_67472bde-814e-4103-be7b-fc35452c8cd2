package com.polarizon.gendo.sg.push.message.api;

import com.alibaba.fastjson.JSONObject;
import com.polarizon.gendo.sg.push.message.bean.bo.AccessTokenBO;
import com.polarizon.gendo.sg.push.message.bean.bo.WechatMessageTemplateBO;
import com.polarizon.gendo.sg.push.message.bean.bo.WechatResponseBO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 微信接口调用api
 *
 *
 */
@FeignClient(url = "${oauth.wx.api.url:https://api.weixin.qq.com}", name = "push-wechar-origin")
public interface WechatOriginControllerFeignClient {
    /**
     * 获取微信网页授权访问token
     *
     * @param appid     应用id
     * @param appsecret 密钥
     * @param code      微信授权code
     * @return
     */
    @GetMapping(value = "/sns/oauth2/access_token?appid={appid}&secret={appsecret}&code={code}&grant_type=authorization_code")
    JSONObject wechatAuthAccessToken(@RequestParam(name = "appid") String appid, @RequestParam(name = "appsecret") String appsecret, @RequestParam(name = "code") String code);

    /**
     * 获取用户信息
     *
     * @param accessToken 网页授权访问token
     * @param openId      用户openId
     * @return
     */
    @GetMapping(value = "/sns/userinfo?access_token={accessToken}&openid={openId}&lang=zh_CN")
    JSONObject wechatUserInfo(@RequestParam("accessToken") String accessToken, @RequestParam("openId") String openId);

    /**
     * 建议调用消息推送微服务中获取访问令牌接口，不要调用此接口获取微信访问令牌 <br/>
     * 由于令牌有效时长默认两小时，所以在消息推送微服务中存储了此令牌 <br/>
     * ps: 单日最多调用微信官方获取令牌服务2000次 <br/>
     * <p>
     * {@see https://developers.weixin.qq.com/doc/offiaccount/Basic_Information/Get_access_token.html}
     *
     * @param appid     应用ID
     * @param appsecret 应用密钥
     * @return
     */
    @GetMapping(value = "/cgi-bin/token?grant_type=client_credential&appid={appid}&secret={appsecret}")
    AccessTokenBO getToken(@RequestParam(name = "appid") String appid, @RequestParam(name = "appsecret") String appsecret);

    /**
     * 发送模板消息 <br/>
     * {@see https://developers.weixin.qq.com/doc/offiaccount/Message_Management/Template_Message_Interface.html}
     *
     * @param accessToken               调用接口凭证
     * @param wechatMessageTemplateBO 模板消息
     * @return
     */
    @PostMapping(value = "/cgi-bin/message/template/send?access_token={accessToken}")
    WechatResponseBO sendMessageTemplate(@RequestParam(name = "accessToken") String accessToken, @RequestBody WechatMessageTemplateBO wechatMessageTemplateBO);
}
