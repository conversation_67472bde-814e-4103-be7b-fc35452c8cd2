package com.polarizon.gendo.sg.push.message.utils;

/**
 * 微信对接常量类
 *
 *
 */
public interface WechatConstants {
    /**
     * 获取微信code url模板常量
     */
    String GET_CODE_URL_TEMPLATE = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=%s" +
            "&redirect_uri=%s&response_type=code&scope=%s&state=STATE#wechat_redirect";

    /**
     * openId key
     */
    String OPEN_ID_KEY = "openid";

    /**
     * 微信昵称key
     */
    String NICKNAME_KEY = "nickname";

    /**
     * 访问token key
     */
    String ACCESS_TOKEN_KEY = "access_token";

    /**
     * 微信访问token缓存key
     */
    String WECHAT_ACCESS_TOKEN_KEY = "wx_access_token";

    /**
     * 事件消息第一行模板
     */
    String EVENT_MESSAGE_FIRST_TEMPLATE = "你好，%s发生%s已经提交，请注意处理：";


    /**
     * 审批通过消息第一行模板
     */
    String PROCESS_PASS_MESSAGE_FIRST_TEMPLATE = "点击下方【我的】进入登录界面";

    /**
     * 审批未通过通过消息第一行模板
     */
    String PROCESS_UNPASS_MESSAGE_FIRST_TEMPLATE = "对不起你的账号未通过审批";

    /**
     * 消息备注
     */
    String EVENT_MESSAGE_REMARK = "点击详情，查看图片！";

    /**
     * 处理人事件详情跳转url
     */
    String DETAIL_URL = "%s/#/eventDetail?eventCode=%s";

    /**
     * 事件消息显示灰色
     */
    String EVENT_MESSAGE_GREY_COLOR = "#A6212121";

    /**
     * 事件消息显示蓝色
     */
    String EVENT_MESSAGE_BLUE_COLOR = "#2C84C2";

    /**
     * 事件消息显示橙色
     */
    String EVENT_MESSAGE_ORANGE_COLOR = "#FFA800";

    // 大写数字
    String[] NUMBERS = {"一","二","三","四","五","六"};
}
