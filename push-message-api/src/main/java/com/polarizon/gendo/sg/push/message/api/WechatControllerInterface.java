package com.polarizon.gendo.sg.push.message.api;

import com.fasterxml.jackson.annotation.JsonView;
import com.polarizon.common.bean.dto.ResultDTO;
import com.polarizon.gendo.sg.authority.bean.dto.LoginResultDTO;
import com.polarizon.gendo.sg.authority.bean.form.UserAuthFormBean;
import com.polarizon.gendo.sg.push.message.bean.bo.EventMessageBO;
import com.polarizon.gendo.sg.push.message.bean.bo.ProcessMessageBO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.io.IOException;

/**
 * 微信对接api接口
 */
@Api(tags = {"微信对接控制器"})
@FeignClient(url = "${com.polarizon.feign.push-message-api-url:http://push-message:20208}/push-message/v1/wechat",
    name = "WechatControllerInterface")
public interface WechatControllerInterface {
    /**
     * 微信授权，接口传递回调地址，回调时返回openId和昵称
     *
     * @param redirectUrl 回调地址
     * @param code        微信授权code，微信回调时传递
     * @throws IOException
     */
    @ApiOperation("微信授权，接口传递回调地址，回调时返回openId和昵称")
    @GetMapping("/auth")
    void auth(@RequestParam("redirectUrl")
              @ApiParam("回调地址") String redirectUrl,
              @RequestParam(value = "code", required = false)
              @ApiParam(value = "微信授权code，微信回调时传递") String code,
              @RequestParam(value = "scope", required = false)
              @ApiParam("微信授权scope，微信回调时传递") String scope) throws IOException;

    /**
     * 微信登录，登录成功后会将openId绑定当前用户，同一个用户只允许绑定一个openId。
     *
     * @param userAuthFormBean 登录鉴权数据
     * @return
     */
    @ApiOperation("微信登录，登录成功后会将openId绑定当前用户，同一个用户只允许绑定一个openId")
    @PostMapping("/login")
    ResultDTO<LoginResultDTO> login(@RequestBody @Validated
                                    @JsonView(UserAuthFormBean.WechatLoginView.class) UserAuthFormBean userAuthFormBean);

    /**
     * 微信解绑
     *
     * @param account 用户账号
     * @return
     */
    @ApiOperation("微信解绑")
    @PutMapping("/unbind")
    ResultDTO unbind(@RequestParam("account") @ApiParam("用户账号") String account);

    /**
     * 事件消息推送
     *
     * @param eventMessageBO 事件消息数据
     * @return
     */
    @ApiOperation("事件消息推送")
    @PostMapping("/push")
    ResultDTO<Object> push(
        @RequestBody @JsonView(EventMessageBO.WechatMessageView.class) EventMessageBO eventMessageBO);

    /**
     * 注册审批消息推送
     *
     * @param processMessageBO 审批消息数据
     * @return
     */
    @ApiOperation("注册审批消息推送")
    @PostMapping("/process/push")
    ResultDTO<Object> processPush(
        @RequestBody ProcessMessageBO processMessageBO);
}
