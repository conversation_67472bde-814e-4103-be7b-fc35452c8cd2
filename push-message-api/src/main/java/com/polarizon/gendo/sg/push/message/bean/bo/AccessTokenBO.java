package com.polarizon.gendo.sg.push.message.bean.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 *
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AccessTokenBO extends WechatResponseBO implements Serializable {
    private static final long serialVersionUID = 1807193343858354570L;

    @JsonProperty(value = "access_token")
    @ApiModelProperty("获取到的访问凭证")
    private String accessToken;

    @JsonProperty(value = "expires_in")
    @ApiModelProperty("凭证有效时间，单位：秒")
    private long expireSin;
}