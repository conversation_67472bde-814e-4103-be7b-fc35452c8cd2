package com.polarizon.gendo.sg.push.message.bean.bo;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.Set;

/**
 * 事件消息对业务对象
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EventMessageBO {
    @NotBlank(message = "事件id不能为空")
    @JsonView({MessageView.class})
    @ApiModelProperty("事件id")
    private String id;

    @JsonView({MessageView.class})
    @ApiModelProperty("事件编码")
    private String eventCode;

    @NotBlank(message = "事件名称不能为空")
    @JsonView({MessageView.class})
    @ApiModelProperty("事件名称")
    private String eventName;

    @ApiModelProperty("设备名称")
    private String deviceName;

    @NotBlank(message = "设备地址不能为空")
    @JsonView({MessageView.class})
    @ApiModelProperty("设备地址")
    private String deviceAddress;

    @NotBlank(message = "事件级别不能为空")
    @JsonView({MessageView.class})
    @ApiModelProperty("事件级别")
    private String eventLevel;

    @NotBlank(message = "事件处理状态不能为空")
    @JsonView({MessageView.class})
    @ApiModelProperty("事件处理状态")
    private String eventHandleStatus;

    @JsonView({WechatMessageView.class})
    @ApiModelProperty("租户id")
    private String tenantId;

    @NotBlank(message = "开始时间不能为空")
    @JsonView({MessageView.class})
    @ApiModelProperty("开始时间")
    private String startTime;

    @JsonView({WechatMessageView.class})
    @ApiModelProperty("结束时间")
    private String endTime;

    @JsonView({MessageView.class})
    @ApiModelProperty("备注")
    private String remark;

    @Size(min = 1, message = "处理人id不能为空")
    @JsonView({MessageView.class})
    @ApiModelProperty("处理人openId")
    private Set<String> handlerOpenIdSet;

    @JsonView({WechatMessageView.class})
    @ApiModelProperty("接收人openId")
    private Set<String> notifierOpenIdSet;

    public interface MessageView {
    }

    public interface WechatMessageView extends MessageView {
    }

    public interface DingtalkMessageView extends MessageView {
    }
}
