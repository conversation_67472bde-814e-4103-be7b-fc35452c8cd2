package com.polarizon.gendo.sg.push.message.bean.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 事件推送模板消息
 *
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("事件推送模板消息")
public class WechatMessageTemplateBO {
    @JsonProperty(value = "touser")
    @ApiModelProperty("接收者openid")
    private String toUser;

    @JsonProperty(value = "template_id")
    @ApiModelProperty("模板ID")
    private String templateId;

    @JsonProperty(value = "url")
    @ApiModelProperty("模板跳转链接（海外帐号没有跳转能力）")
    private String url;

    @JsonProperty(value = "topcolor")
    @ApiModelProperty("标题颜色")
    private String topColor;

    @JsonProperty(value = "miniprogram")
    @ApiModelProperty("跳小程序所需数据，不需跳小程序可不用传该数据")
    private Miniprogram miniprogram;

    @JsonProperty("data")
    @ApiModelProperty("模板数据")
    private TemplateData data;

    @Data
    public static class Miniprogram {
        @JsonProperty(value = "appid")
        @ApiModelProperty("所需跳转到的小程序appid")
        private String appId;

        @JsonProperty(value = "pagepath")
        @ApiModelProperty("所需跳转到小程序的具体页面路径，支持带参数,（示例index?foo=bar），要求该小程序已发布，暂不支持小游戏")
        private String pagePath;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel("模板数据")
    public static class TemplateData {
        private TemplateDataStyle first;
        private TemplateDataStyle keyword1;
        private TemplateDataStyle keyword2;
        private TemplateDataStyle keyword3;
        private TemplateDataStyle keyword4;
        private TemplateDataStyle keyword5;
        private TemplateDataStyle remark;

        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        @ApiModel("模板数据风格")
        public static class TemplateDataStyle {

            private String value;

            @ApiModelProperty("模板内容字体颜色，不填默认为黑色")
            private String color;

        }

    }

}
