package com.polarizon.gendo.sg.push.message.bean.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * 邮件消息业务对象
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MailMessageBO {
    @NotEmpty(message = "收件人不能为空")
    @ApiModelProperty("收件人")
    private List<String> toAddress;

    @NotNull(message = "业务类型不能为空")
    @ApiModelProperty("业务类型")
    private BusinessType businessType;

    @ApiModelProperty("其它参数信息，比如审批拒绝的原因")
    private Map<String, Object> others;

    /**
     * 业务类型
     */
    public enum BusinessType {
        /**
         * 注册成功
         */
        RegisterSuccess(1, "账号通过审批", "EmailRegisterSuccess"),
        /**
         * 注册失败
         */
        RegisterFail(3, "账号未通过审批", "EmailRegisterFail");

        int value;
        String subject;
        String template;

        private BusinessType(int value, String subject, String template) {
            this.value = value;
            this.subject = subject;
            this.template = template;
        }

        public int getValue() {
            return this.value;
        }

        public String getSubject() {
            return this.subject;
        }

        public String getTemplate() {
            return this.template;
        }
    }
}
