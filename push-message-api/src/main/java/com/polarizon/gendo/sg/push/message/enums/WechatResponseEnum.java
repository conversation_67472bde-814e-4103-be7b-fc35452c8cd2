package com.polarizon.gendo.sg.push.message.enums;

import com.google.common.collect.Lists;
import com.polarizon.common.exception.asserts.BusinessExceptionAssert;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 微信对接异常枚举类
 *
 *
 */
@Getter
@AllArgsConstructor
public enum WechatResponseEnum implements BusinessExceptionAssert {
    /**
     * 获取访问令牌失败断言
     */
    WECHAT_ACCESS_TOKEN_ERROR(-1, "获取访问令牌失败", null),

    /**
     * 文件上传失败断言
     */
    FILE_UPLOAD_ERROR(-1, "文件上传失败", Lists.newArrayList());

    private int code;
    private String message;
    private Object errorData;
}
