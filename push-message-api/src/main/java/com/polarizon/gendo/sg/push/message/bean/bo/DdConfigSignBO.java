package com.polarizon.gendo.sg.push.message.bean.bo;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 钉钉签名配置
 *
 */

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DdConfigSignBO {
/**
     * 钉钉签名
     */
    @Parameter(description = "钉钉签名")
    public String signature;

    /**
     * 时间戳
     */
    @Parameter(description = "时间戳")
    public String timestamp;

    /**
     * 随机字符串
     */
    @Parameter(description = "随机字符串")
    public String nonceStr;

    /**
     * 企业id
     */
    @Parameter(description = "企业id")
    public String corpId;

    /**
     * 应用id
     */
    @Parameter(description = "应用id")
    public String agentId;

    /**
     * jsapi_ticket
     */
    @Parameter(description = "jsapi_ticket")
    public String jsTicket;
}
