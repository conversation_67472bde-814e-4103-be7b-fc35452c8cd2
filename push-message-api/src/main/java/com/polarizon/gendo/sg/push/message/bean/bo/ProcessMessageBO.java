package com.polarizon.gendo.sg.push.message.bean.bo;

import com.polarizon.gendo.sg.authority.enums.ProcessStatus;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

/**
 * 注册审批消息对业务对象
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProcessMessageBO {
    @ApiModelProperty("审核状态")
    private ProcessStatus process;

    @ApiModelProperty("审批备注")
    private String remark;

    @ApiModelProperty("所属组织")
    private String org;

    @ApiModelProperty("审批人")
    private String reviewer;

    @ApiModelProperty("审批时间")
    private Long time;

    @ApiModelProperty("接收人openId")
    private Set<String> notifierOpenIdSet;
}
