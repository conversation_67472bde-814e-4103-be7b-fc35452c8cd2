package com.polarizon.gendo.sg.push.message.bean.bo;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class JsapiTicketBO {
    /**
     * jsapi_ticket
     */
    @Parameter(description = "jsapi_ticket")
    public String ticket;

    /**
     * 过期时间
     */
    @Parameter(description = "过期时间")
    public Long expiresIn;
}
