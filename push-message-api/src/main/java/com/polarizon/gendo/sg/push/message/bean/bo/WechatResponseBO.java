package com.polarizon.gendo.sg.push.message.bean.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("wechat接口响应信息")
public class WechatResponseBO {
    @JsonProperty("errcode")
    @ApiModelProperty("错误码 0代表成功，其他值表示失败")
    private int errCode;

    @JsonProperty("errmsg")
    @ApiModelProperty("错误详细信息")
    private String errMsg;
}
