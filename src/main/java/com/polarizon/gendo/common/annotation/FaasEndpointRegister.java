package com.polarizon.gendo.common.annotation;

import org.springframework.web.bind.annotation.RequestMethod;

import java.lang.annotation.*;

import static org.springframework.web.bind.annotation.RequestMethod.POST;

@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface FaasEndpointRegister {
    // 注册路径
    String[] path();

    // 请求类型
    String[] consumes() default {};
    
    // 请求方法
    RequestMethod method() default POST;
}
