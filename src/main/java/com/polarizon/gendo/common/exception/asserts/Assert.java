package com.polarizon.gendo.common.exception.asserts;

import com.polarizon.gendo.common.exception.BaseException;
import jakarta.validation.constraints.NotNull;
import org.apache.commons.lang3.StringUtils;
import org.springframework.lang.Nullable;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.Map;
import java.util.Objects;

public interface Assert {
    /**
     * 创建异常
     *
     * @param args message占位符对应的参数列表（异常信息支持传递参数方式，避免在判断之前进行字符串拼接操作）
     * @return BaseException 异常实例
     */
    BaseException newException(Object... args);

    /**
     * 创建异常
     *
     * @param t    异常实例
     * @param args message占位符对应的参数列表（异常信息支持传递参数方式，避免在判断之前进行字符串拼接操作）
     * @return BaseException 异常实例
     */
    BaseException newException(Throwable t, Object... args);


    /**
     * 断言对象非空。如果对象为空，则抛出异常
     * 异常信息支持传递参数方式，避免在判断之前进行字符串拼接操作
     *
     * @param obj  待判断对象
     * @param args message占位符对应的参数列表（异常信息支持传递参数方式，避免在判断之前进行字符串拼接操作）
     */
    default void assertNotNull(Object obj, Object... args) {
        if (obj == null) {
            throw newException(args);
        }
    }

    /**
     * 断言集合不是所有元素为空。如果所有元素为空，则抛出异常
     * 异常信息支持传递参数方式，避免在判断之前进行字符串拼接操作
     *
     * @param collection 待判断对象
     * @param args       message占位符对应的参数列表（异常信息支持传递参数方式，避免在判断之前进行字符串拼接操作）
     */
    default <T> void assertNotAllNull(@NotNull Collection<T> collection, Object... args) {
        assertNotEmpty(collection, args);
        for (T o : collection) {
            if (o != null) {
                return;
            }
        }
        throw newException(args);
    }

    /**
     * 断言集合非空。
     *
     * @param collection 待判断对象
     * @param args       message占位符对应的参数列表（异常信息支持传递参数方式，避免在判断之前进行字符串拼接操作）
     */
    default <T> void assertNotEmpty(Collection<T> collection, Object... args) {
        if (CollectionUtils.isEmpty(collection)) {
            throw newException(args);
        }
    }

    /**
     * 断言Map非空。
     *
     * @param map  待判断对象
     * @param args message占位符对应的参数列表（异常信息支持传递参数方式，避免在判断之前进行字符串拼接操作）
     */
    default <K, V> void assertNotEmpty(Map<K, V> map, Object... args) {
        if (CollectionUtils.isEmpty(map)) {
            throw newException(args);
        }
    }

    /**
     * 断言字符串非空。
     *
     * @param arg 待判断对象
     */
    default void assertNotEmpty(CharSequence arg) {
        if (StringUtils.isEmpty(arg)) {
            throw newException(arg);
        }
    }

    /**
     * 断言字符串非空。
     *
     * @param arg  待判断对象
     * @param args message占位符对应的参数列表（异常信息支持传递参数方式，避免在判断之前进行字符串拼接操作）
     */
    default void assertNotEmpty(CharSequence arg, Object... args) {
        if (StringUtils.isEmpty(arg)) {
            throw newException(args);
        }
    }

    /**
     * 断言字符串非空白。
     *
     * @param arg  待判断对象
     * @param args message占位符对应的参数列表（异常信息支持传递参数方式，避免在判断之前进行字符串拼接操作）
     */
    default void assertNotBlank(CharSequence arg, @Nullable Object... args) {
        if (StringUtils.isBlank(arg)) {
            throw newException(args);
        }
    }

    /**
     * 断言字符串非空白。
     *
     * @param arg 待判断对象
     */
    default void assertNotBlank(CharSequence arg) {
        if (StringUtils.isBlank(arg)) {
            throw newException(arg);
        }
    }

    /**
     * 断言两个对象不相等
     *
     * @param obj1 待判断对象1
     * @param obj2 待判断对象2
     * @param args message占位符对应的参数列表（异常信息支持传递参数方式，避免在判断之前进行字符串拼接操作）
     */
    default void assertNotEqual(@Nullable Object obj1, @Nullable Object obj2, @Nullable Object... args) {
        if (Objects.equals(obj1, obj2)) {
            throw newException(args);
        }
    }

    /**
     * 断言两个对象相等
     *
     * @param obj1 待判断对象1
     * @param obj2 待判断对象2
     * @param args message占位符对应的参数列表（异常信息支持传递参数方式，避免在判断之前进行字符串拼接操作）
     */
    default void assertEqual(@Nullable Object obj1, @Nullable Object obj2, @Nullable Object... args) {
        if (!Objects.equals(obj1, obj2)) {
            throw newException(args);
        }
    }

    /**
     * 断言是否为真
     *
     * @param obj  待判断对象
     * @param args message占位符对应的参数列表（异常信息支持传递参数方式，避免在判断之前进行字符串拼接操作）
     */
    default void assertTrue(boolean obj, @Nullable Object... args) {
        if (!obj) {
            throw newException(args);
        }
    }

    /**
     * 断言是否为假
     *
     * @param obj  待判断对象
     * @param args message占位符对应的参数列表（异常信息支持传递参数方式，避免在判断之前进行字符串拼接操作）
     */
    default void assertFalse(boolean obj, @Nullable Object... args) {
        if (obj) {
            throw newException(args);
        }
    }

    /**
     * 断言集合为空。
     *
     * @param collection 待判断对象
     * @param args       message占位符对应的参数列表（异常信息支持传递参数方式，避免在判断之前进行字符串拼接操作）
     */
    default <T> void assertEmpty(@Nullable Collection<T> collection, @Nullable Object... args) {
        if (!CollectionUtils.isEmpty(collection)) {
            throw newException(args);
        }
    }

    /**
     * 断言对象为空。
     *
     * @param t    待判断对象
     * @param args message占位符对应的参数列表（异常信息支持传递参数方式，避免在判断之前进行字符串拼接操作）
     */
    default <T> void assertEmpty(T t, @Nullable Object... args) {
        if (t != null) {
            throw newException(args);
        }
    }

    /**
     * 断言对象为空。
     *
     * @param t    待判断对象
     * @param args message占位符对应的参数列表（异常信息支持传递参数方式，避免在判断之前进行字符串拼接操作）
     */
    default <T> void assertNull(T t, @Nullable Object... args) {
        if (t != null) {
            throw newException(args);
        }
    }

    /**
     * 断言char序列为空。<p/>
     * Checks if a CharSequence is not empty ("") and not null.
     *
     * <pre>
     * StringUtils.isNotEmpty(null)      = false
     * StringUtils.isNotEmpty("")        = false
     * StringUtils.isNotEmpty(" ")       = true
     * StringUtils.isNotEmpty("bob")     = true
     * StringUtils.isNotEmpty("  bob  ") = true
     * </pre>
     *
     * @param cs   待判断char序列
     * @param args message占位符对应的参数列表（异常信息支持传递参数方式，避免在判断之前进行字符串拼接操作）
     */
    default void assertEmpty(CharSequence cs, @Nullable Object... args) {
        if (StringUtils.isNotEmpty(cs)) {
            throw newException(args);
        }
    }

    /**
     * 断言char序列为空白。<p/>
     * Checks if a CharSequence is not empty (""), not null and not whitespace only.
     *
     * <pre>
     * StringUtils.isNotBlank(null)      = false
     * StringUtils.isNotBlank("")        = false
     * StringUtils.isNotBlank(" ")       = false
     * StringUtils.isNotBlank("bob")     = true
     * StringUtils.isNotBlank("  bob  ") = true
     * </pre>
     *
     * @param cs   待判断char序列
     * @param args message占位符对应的参数列表（异常信息支持传递参数方式，避免在判断之前进行字符串拼接操作）
     */
    default void assertBlank(CharSequence cs, @Nullable Object... args) {
        if (StringUtils.isNotBlank(cs)) {
            throw newException(args);
        }
    }

}