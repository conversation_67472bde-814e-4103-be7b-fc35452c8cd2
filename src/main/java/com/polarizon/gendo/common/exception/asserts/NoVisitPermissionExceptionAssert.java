package com.polarizon.gendo.common.exception.asserts;


import com.polarizon.gendo.common.exception.BaseException;
import com.polarizon.gendo.common.exception.NoVisitPermissionException;
import com.polarizon.gendo.common.exception.enums.IResponseEnum;

import java.text.MessageFormat;

/**
 * 没有访问权限断言
 *
 * <AUTHOR>
 */
public interface NoVisitPermissionExceptionAssert extends IResponseEnum, Assert {

    @Override
    default BaseException newException(Object... args) {
        String msg = MessageFormat.format(this.getMessage(), args);

        return new NoVisitPermissionException(this, args, msg);
    }

    @Override
    default BaseException newException(Throwable t, Object... args) {
        String msg = MessageFormat.format(this.getMessage(), args);

        return new NoVisitPermissionException(this, args, msg, t);
    }

}