package com.polarizon.gendo.common.exception.asserts;

import com.polarizon.gendo.common.exception.BaseException;
import com.polarizon.gendo.common.exception.BusinessException;
import com.polarizon.gendo.common.exception.enums.IResponseEnum;

import java.text.MessageFormat;

/**
 * 业务异常断言
 */
public interface BusinessExceptionAssert extends IResponseEnum, Assert {

    @Override
    default BaseException newException(Object... args) {
        String msg = MessageFormat.format(this.getMessage(), args);

        return new BusinessException(this, args, msg);
    }

    @Override
    default BaseException newException(Throwable t, Object... args) {
        String msg = MessageFormat.format(this.getMessage(), args);

        return new BusinessException(this, args, msg, t);
    }

}