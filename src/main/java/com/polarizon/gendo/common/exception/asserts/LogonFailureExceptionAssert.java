package com.polarizon.gendo.common.exception.asserts;


import com.polarizon.gendo.common.exception.BaseException;
import com.polarizon.gendo.common.exception.LogonFailureException;
import com.polarizon.gendo.common.exception.enums.IResponseEnum;

import java.text.MessageFormat;

/**
 * 登录失效异常断言
 *
 * <AUTHOR>
 */
public interface LogonFailureExceptionAssert extends IResponseEnum, Assert {

    @Override
    default BaseException newException(Object... args) {
        String msg = MessageFormat.format(this.getMessage(), args);

        return new LogonFailureException(this, args, msg);
    }

    @Override
    default BaseException newException(Throwable t, Object... args) {
        String msg = MessageFormat.format(this.getMessage(), args);

        return new LogonFailureException(this, args, msg, t);
    }

}