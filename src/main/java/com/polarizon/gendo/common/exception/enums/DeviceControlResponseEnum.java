package com.polarizon.gendo.common.exception.enums;

import com.polarizon.gendo.common.exception.asserts.BusinessExceptionAssert;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum DeviceControlResponseEnum implements BusinessExceptionAssert {
    DRIVE_NOT_FOUNT(1001, "找不到合适的驱动",null);

    private int code;
    private String message;
    private Object errorData;
}
