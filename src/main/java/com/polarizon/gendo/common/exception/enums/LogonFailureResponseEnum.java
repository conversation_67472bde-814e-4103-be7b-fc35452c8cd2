package com.polarizon.gendo.common.exception.enums;

import com.polarizon.gendo.common.exception.asserts.LogonFailureExceptionAssert;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 登录异常枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum LogonFailureResponseEnum implements LogonFailureExceptionAssert {

    NO_TOKEN_ERROR(-1, "没有登录，请先进行登录", null),
    TOKEN_FAILURE_ERROR(-1, "登录过期，请重新登录", null);

    private int code;
    private String message;
    private Object errorData;
}