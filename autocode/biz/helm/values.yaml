replicaCount: 1
image:
  repository: polarizon-rag/kb-manage-polarinsight
  pullPolicy: IfNotPresent
  tag: 1.0.0-beta.4-alpha
service:
  port: 20501
  name: kb-manage-polarinsight
traefik:
  # 集成CI为true，单独跑CI为false
  install: false
  ingressroute:
    enabled: true
resources:
  limits:
    cpu: 4
    memory: 4Gi
  requests:
    cpu: 1
    memory: 3Gi
  

global:
  configMap:
    HOST_PATH: "/ai-data"
    CONTAINER_PATH: "/ai-data"
    DOCKER_API_URL: "unix:///var/run/docker.sock"
    REGISTRY_URL: "https://**************:30443/api/v2.0"
    REGISTRY_ADDRESS: "**************:30443"
    SSH_PORT: 22
    JUPYTER_PORT: 8888
    NOMACHINE_PORT: 4000
    SHM_SIZE: "32G"
    JAVA_OPTS: "-XX:MaxRAMPercentage=60.0 -XX:+HeapDumpOnOutOfMemoryError -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:56634"
    SPRING_DATA_MONGODB_URI: "mongodb://root:<EMAIL>:27017,mongodb-1.mongodb-headless.middleware.svc.cluster.local:27017"
    timezone: Asia/Shanghai
    SPRING_DATA_REDIS_CLUSTER_NODES: redis-cluster-headless:6379
    SPRING_PROFILES_ACTIVE: prod
    ASSET_QUOTA_ENABLE: false
  docker:
    socket: /var/run/docker.sock 
  image:
    registry: hub.polarise.cn
  traefik:
    # 集成CI为true，单独跑CI为false
    install: false
    ingressroute:
      enabled: true