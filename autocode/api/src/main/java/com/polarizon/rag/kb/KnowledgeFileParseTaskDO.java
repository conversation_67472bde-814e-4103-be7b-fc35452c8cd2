package com.polarizon.rag.kb;

import static org.springframework.data.mongodb.core.mapping.FieldType.OBJECT_ID;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonView;
import com.polarizon.gendo.common.annotation.AfterInit;
import com.polarizon.gendo.common.bo.AbstractBaseBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.springframework.data.annotation.ReadOnlyProperty;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;


import com.polarizon.rag.kb.enums.KbFileParseStatusEnum;
import com.polarizon.rag.kb.enums.KnowledgeErrorHandlerEnum;
import java.util.List;
import com.polarizon.rag.enums.GroupTypeEnum;


@Schema(description = "知识库文件解析任务")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Document("#{@namespaceProvider.getNamespace()}_com.polarizon.rag.kb.KnowledgeFileParseTaskDO")
@CompoundIndexes({
	@CompoundIndex(name = "kbBaseID_1", def = "{'knowledgeBaseID':1}"),
	@CompoundIndex(name = "kbFileID_1", def = "{'knowledgeFileID':1}"),
	@CompoundIndex(name = "kbEmbdConfigID_1", def = "{'kbEmbdConfigID':1}"),

	@CompoundIndex(name = "createTime_index", def = "{'createTime':1}"),
	@CompoundIndex(name = "updateTime", def = "{'updateTime':1}"),
    @CompoundIndex(name = "_id", def = "{'_id':1}")
})
public class KnowledgeFileParseTaskDO extends AbstractBaseBO {

       
    /**
     * avro type: *schema.StringField
     */
    @Schema(description = "知识库ID")
    @JsonView({EditView.class})
    @JsonProperty("knowledgeBaseID")
    @Field(name="knowledgeBaseID", targetType = OBJECT_ID)
    public String knowledgeBaseID;
    
       
    /**
     * avro type: *schema.StringField
     */
    @Schema(description = "文件ID")
    @JsonView({EditView.class})
    @JsonProperty("knowledgeFileID")
    @Field(name="knowledgeFileID", targetType = OBJECT_ID)
    public String knowledgeFileID;
    
       
    /**
     * avro type: *schema.StringField
     */
    @Schema(description = "向量化配置ID")
    @JsonView({EditView.class})
    @JsonProperty("kbEmbdConfigID")
    @Field(name="kbEmbdConfigID", targetType = OBJECT_ID)
    public String kbEmbdConfigID;
    
     
    /**
     * avro type: *schema.StringField
     */
    @Schema(description = "文件名")
    @JsonView({EditView.class})
    @JsonProperty("fileName")
    @Field("fileName")
    public String fileName;
     
    /**
     * avro type: *schema.Reference
     */
    @Schema(description = "任务状态")
    @JsonView({EditView.class})
    @JsonProperty("status")
    @Field("status")
    public KbFileParseStatusEnum status;
     
    /**
     * avro type: *schema.Reference
     */
    @Schema(description = "错误处理策略")
    @JsonView({EditView.class})
    @JsonProperty("errorHandler")
    @Field("errorHandler")
    public KnowledgeErrorHandlerEnum errorHandler;
     
    /**
     * avro type: *schema.IntField
     */
    @Schema(description = "最大重试次数")
    @JsonView({EditView.class})
    @JsonProperty("maxRetryCount")
    @Field("maxRetryCount")
    public Integer maxRetryCount;
     
    /**
     * avro type: *schema.IntField
     */
    @Schema(description = "当前重试次数")
    @JsonView({EditView.class})
    @JsonProperty("currentRetryCount")
    @Field("currentRetryCount")
    public Integer currentRetryCount;
     
    /**
     * avro type: *schema.StringField
     */
    @Schema(description = "错误信息")
    @JsonView({EditView.class})
    @JsonProperty("errorMessage")
    @Field("errorMessage")
    public String errorMessage;
     
    /**
     * avro type: *schema.LongField
     */
    @Schema(description = "下次重试时间")
    @JsonView({EditView.class})
    @JsonProperty("nextRetryTime")
    @Field("nextRetryTime")
    public Long nextRetryTime;
     
    /**
     * avro type: *schema.ArrayField
     */
    @Schema(description = "组ID列表")
    @JsonView({EditView.class})
    @JsonProperty("groupIDs")
    @Field("groupIDs")
    public List<String> groupIDs;
     
    /**
     * avro type: *schema.ArrayField
     */
    @Schema(description = "组类型列表: ME-个人, ORG-组织")
    @JsonView({EditView.class})
    @JsonProperty("groupType")
    @Field("groupType")
    public List<GroupTypeEnum> groupType;


}
