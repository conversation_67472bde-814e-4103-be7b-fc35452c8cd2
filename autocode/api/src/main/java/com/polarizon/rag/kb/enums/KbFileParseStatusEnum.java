package com.polarizon.rag.kb.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * PENDING-等待中, RUNNING-执行中, COMPLETED-已完成, FAILED-执行失败, RETRYING-重试中
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum KbFileParseStatusEnum {

    PENDING("PENDING"),

    RUNNING("RUNNING"),

    COMPLETED("COMPLETED"),

    FAILED("FAILED"),

    RETRYING("RETRYING");

    private String name;
}
