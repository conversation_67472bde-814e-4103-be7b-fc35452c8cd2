package com.polarizon.rag.kb.params;

import com.alibaba.fastjson.JSONObject;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.lang3.BooleanUtils;
import org.springframework.data.mongodb.core.query.Criteria;

import com.polarizon.gendo.common.utils.QueryUtil;
import com.polarizon.gendo.common.utils.FieldValidationUtil;
import com.polarizon.gendo.common.bo.FuzzySearchGroup;

import static com.polarizon.gendo.common.bo.AbstractBaseBO.StatusFlag.DEAD;
import static com.polarizon.gendo.common.bo.AbstractBaseBO.StatusFlag.LIVE;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static org.apache.commons.collections4.CollectionUtils.isEmpty;
import static org.apache.commons.collections4.CollectionUtils.isNotEmpty;
import static org.apache.commons.collections4.CollectionUtils.containsAny;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

import java.util.List;
import java.util.Objects;

      import com.polarizon.rag.kb.enums.KbFileParseStatusEnum;
import com.polarizon.rag.kb.enums.KnowledgeErrorHandlerEnum;
import com.polarizon.rag.enums.GroupTypeEnum;

import com.polarizon.rag.kb.KnowledgeFileParseTaskDO;
import com.polarizon.gendo.common.bo.AbstractBaseBO.StatusFlag;
import com.polarizon.gendo.common.bo.BaseParams;
import com.polarizon.gendo.common.utils.RegexUtil;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
public class KnowledgeFileParseTaskDOParams extends BaseParams {
	
    @Parameter(description = "软删除标志")
    List<StatusFlag> statusFlag;

    @Parameter(description = "模糊搜索组列表，可以指定多个组，每组包含关键词和要搜索的字段列表，组间是AND关系，组内字段是OR关系") 
    List<FuzzySearchGroup> fuzzySearchGroups;
    
    @Parameter(description = "知识库ID") 
    public List<String> knowledgeBaseID;
    
    
    @Parameter(description = "文件ID") 
    public List<String> knowledgeFileID;
    
    
    @Parameter(description = "向量化配置ID") 
    public List<String> kbEmbdConfigID;
    
    
    @Parameter(description = "文件名") 
    public List<String> fileName;
    
    @Parameter(description = "模糊查询参数: 文件名") 
    String fileNameLike;
    
    @Parameter(description = "任务状态") 
    public List<KbFileParseStatusEnum> status;
    
    
    @Parameter(description = "错误处理策略") 
    public List<KnowledgeErrorHandlerEnum> errorHandler;
    
    
    @Parameter(description = "最大重试次数") 
    public List<Integer> maxRetryCount;
    
    
    @Parameter(description = "当前重试次数") 
    public List<Integer> currentRetryCount;
    
    
    @Parameter(description = "错误信息") 
    public List<String> errorMessage;
    
    @Parameter(description = "模糊查询参数: 错误信息") 
    String errorMessageLike;
    
    @Parameter(description = "下次重试时间") 
    public List<Long> nextRetryTime;
    
    
    @Parameter(description = "组ID列表")
    public List<String> groupIDs;
    @Parameter(description = "不为空查询参数: 组ID列表") 
    public Boolean groupIDsNotEmpty;
    
    
    @Parameter(description = "组类型列表: ME-个人, ORG-组织")
    public List<GroupTypeEnum> groupType;
    @Parameter(description = "不为空查询参数: 组类型列表: ME-个人, ORG-组织") 
    public Boolean groupTypeNotEmpty;
    

    public void andCriteria(Criteria criteria) {
        andCriteria(criteria, null);
        andElementCriteria(criteria, null);
    }

    public void andCountCriteria(Criteria criteria) {
        andCriteria(criteria, null);
        andElementCriteria(criteria, null);
    }

    public void andCriteria(Criteria criteria, String prefix) {
        super.baseAndCriteria(criteria, prefix);// 处理多组字段的模糊搜索
        if (isNotEmpty(fuzzySearchGroups)) {
            // 验证实体类字段，防止SQL注入
            String className = "KnowledgeFileParseTaskDO";
            try {
                Class<?> entityClass = Class.forName("com.polarizon.rag.kb.KnowledgeFileParseTaskDO");
                for (FuzzySearchGroup group : fuzzySearchGroups) {
                    if (group != null) {
                        // 验证字段有效性
                        List<String> validFields = FieldValidationUtil.validateAndFilterFields(group.getFields(), entityClass);
                        group.setFields(validFields);
                        
                        // 应用有效的模糊搜索条件
                        Criteria groupCriteria = group.createCriteria(prefix);
                        if (groupCriteria != null) {
                            criteria.andOperator(groupCriteria);
                        }
                    }
                }
            } catch (ClassNotFoundException e) {
                System.err.println("无法找到实体类: " + className + ", " + e.getMessage());
            }
        }

        if (isNotEmpty(knowledgeBaseID)) {
            criteria.and(concatDot(prefix, "knowledgeBaseID")).in(knowledgeBaseID);
        }

        if (isNotEmpty(knowledgeFileID)) {
            criteria.and(concatDot(prefix, "knowledgeFileID")).in(knowledgeFileID);
        }

        if (isNotEmpty(kbEmbdConfigID)) {
            criteria.and(concatDot(prefix, "kbEmbdConfigID")).in(kbEmbdConfigID);
        }

        if (isNotEmpty(fileName)) {
            criteria.and(concatDot(prefix, "fileName")).in(fileName);
        }
        if (isNotBlank(fileNameLike)) { 
            criteria.and(concatDot(prefix, "fileName")).regex(".*?" + RegexUtil.escapeRegexCharacter(fileNameLike) + ".*");
        }

        if (isNotEmpty(status)) {
            criteria.and(concatDot(prefix, "status")).in(status);
        }

        if (isNotEmpty(errorHandler)) {
            criteria.and(concatDot(prefix, "errorHandler")).in(errorHandler);
        }

        if (isNotEmpty(maxRetryCount)) {
            criteria.and(concatDot(prefix, "maxRetryCount")).in(maxRetryCount);
        }

        if (isNotEmpty(currentRetryCount)) {
            criteria.and(concatDot(prefix, "currentRetryCount")).in(currentRetryCount);
        }

        if (isNotEmpty(errorMessage)) {
            criteria.and(concatDot(prefix, "errorMessage")).in(errorMessage);
        }
        if (isNotBlank(errorMessageLike)) { 
            criteria.and(concatDot(prefix, "errorMessage")).regex(".*?" + RegexUtil.escapeRegexCharacter(errorMessageLike) + ".*");
        }

        if (isNotEmpty(nextRetryTime)) {
            criteria.and(concatDot(prefix, "nextRetryTime")).in(nextRetryTime);
        }

        if(isNotEmpty(groupIDs)) {
            criteria.and(concatDot(prefix, "groupIDs")).in(groupIDs);
        } else if (Objects.nonNull(groupIDsNotEmpty)) {
            if (groupIDsNotEmpty) {
                criteria.and(concatDot(prefix, "groupIDs")).exists(true).ne(List.of());
            } else {
                criteria.orOperator(Criteria.where(concatDot(prefix, "groupIDs")).is(null),
                    Criteria.where(concatDot(prefix, "groupIDs")).exists(false),
                    Criteria.where(concatDot(prefix, "groupIDs")).size(0));
            }
        }

        if(isNotEmpty(groupType)) {
            criteria.and(concatDot(prefix, "groupType")).in(groupType);
        } else if (Objects.nonNull(groupTypeNotEmpty)) {
            if (groupTypeNotEmpty) {
                criteria.and(concatDot(prefix, "groupType")).exists(true).ne(List.of());
            } else {
                criteria.orOperator(Criteria.where(concatDot(prefix, "groupType")).is(null),
                    Criteria.where(concatDot(prefix, "groupType")).exists(false),
                    Criteria.where(concatDot(prefix, "groupType")).size(0));
            }
        }
        
        if (isNotBlank(prefix)){
            andElementCriteria(criteria, prefix);
        }
    }

    private void andElementCriteria(Criteria criteria, String prefix){            
    }

    public Criteria getElementCountCriteria(String prefix) {
        Criteria criteria = new Criteria();            
        return criteria;
    }

    public boolean filter(KnowledgeFileParseTaskDO obj) {
        if (!super.filter(obj)){
            return false;
        } 
        if (isNotEmpty(knowledgeBaseID) && !containsAny(knowledgeBaseID, obj.knowledgeBaseID)) {
            return false;
        }
        
         
        if (isNotEmpty(knowledgeFileID) && !containsAny(knowledgeFileID, obj.knowledgeFileID)) {
            return false;
        }
        
         
        if (isNotEmpty(kbEmbdConfigID) && !containsAny(kbEmbdConfigID, obj.kbEmbdConfigID)) {
            return false;
        }
        
         
        if (isNotEmpty(fileName) && !containsAny(fileName, obj.fileName)) {
            return false;
        }
        if (isNotBlank(this. fileNameLike) && !obj.fileName.contains(this.fileNameLike)) {
            return false;
        }
         
        if (isNotEmpty(status) && !containsAny(status, obj.status)) {
            return false;
        }
        
         
        if (isNotEmpty(errorHandler) && !containsAny(errorHandler, obj.errorHandler)) {
            return false;
        }
        
         
        if (isNotEmpty(maxRetryCount) && !containsAny(maxRetryCount, obj.maxRetryCount)) {
            return false;
        }
        
         
        if (isNotEmpty(currentRetryCount) && !containsAny(currentRetryCount, obj.currentRetryCount)) {
            return false;
        }
        
         
        if (isNotEmpty(errorMessage) && !containsAny(errorMessage, obj.errorMessage)) {
            return false;
        }
        if (isNotBlank(this. errorMessageLike) && !obj.errorMessage.contains(this.errorMessageLike)) {
            return false;
        }
         
        if (isNotEmpty(nextRetryTime) && !containsAny(nextRetryTime, obj.nextRetryTime)) {
            return false;
        }
        
         
        if (isNotEmpty(groupIDs) && !containsAny(groupIDs, obj.groupIDs)) {
            return false;
        }
        
         
        if (isNotEmpty(groupType) && !containsAny(groupType, obj.groupType)) {
            return false;
        }
        
        

        return true;
    }

    @Override
    public Class<?> getEntityClass() {
        return KnowledgeFileParseTaskDO.class;
    }
}
