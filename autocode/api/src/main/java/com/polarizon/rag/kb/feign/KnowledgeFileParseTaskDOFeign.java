package com.polarizon.rag.kb.feign;

import com.alibaba.fastjson.JSONObject;
import com.polarizon.rag.kb.KnowledgeFileParseTaskDO;
import com.polarizon.rag.kb.params.KnowledgeFileParseTaskDOParams;
import com.polarizon.rag.kb.update.KnowledgeFileParseTaskDOUpdate;

import com.polarizon.rag.kb.enums.KbFileParseStatusEnum;
import com.polarizon.rag.kb.enums.KnowledgeErrorHandlerEnum;
import com.polarizon.rag.enums.GroupTypeEnum;

import java.util.List;
import java.util.Map;

import org.hibernate.validator.constraints.Length;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import com.fasterxml.jackson.annotation.JsonView;
import com.polarizon.gendo.common.bo.AbstractBaseBO;
import com.polarizon.gendo.common.bo.AbstractBaseBO.EditView;
import com.polarizon.gendo.common.bo.AbstractBaseBO.ShowView;
import com.polarizon.gendo.common.bo.AbstractBaseBO.StatusFlag;
import com.polarizon.gendo.common.bo.AbstractBaseBO.UpdateAction;
import com.polarizon.gendo.common.dto.ResultDTO;
import com.polarizon.gendo.common.dto.ValidatedList;
import com.polarizon.gendo.common.utils.Constants;
import com.polarizon.gendo.common.bo.NotifyParams;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;

@FeignClient(url = "${com.polarizon.feign.kb-manage-polarinsight-api-url:http://kb-manage-polarinsight:20501}/kb-manage-polarinsight/v1/knowledgefileparsetask", name = "KnowledgeFileParseTaskDOFeign")
public interface KnowledgeFileParseTaskDOFeign {
    @PostMapping
    @Operation(summary = "新增")
    @JsonView(AbstractBaseBO.ShowView.class)
    @Validated(AbstractBaseBO.CreateAction.class)
    public ResultDTO<KnowledgeFileParseTaskDO> add(
		@Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
		@Parameter(description = "请求用户账号", example = "admin") @RequestHeader(required = true, value = Constants.HEADER_ACCOUNT_KEY) @NotBlank  String userAccount,
		@JsonView(AbstractBaseBO.EditView.class) @RequestBody() @Valid KnowledgeFileParseTaskDO bean);

    @PostMapping
    @Operation(summary = "新增")
    @JsonView(AbstractBaseBO.ShowView.class)
    @Validated(AbstractBaseBO.CreateAction.class)
    public ResultDTO<KnowledgeFileParseTaskDO> add(
        @Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
        @Parameter(description = "请求用户账号", example = "admin") @RequestHeader(required = true, value = Constants.HEADER_ACCOUNT_KEY) @NotBlank  String userAccount,
        @Parameter(description = "数据标签")
        @RequestHeader(required = false, value = Constants.HEADER_DATA_TAG_KEY)
        List<String> dataTag,
        @Parameter(description = "领域标签")
        @RequestHeader(required = false, value = Constants.HEADER_DOMAIN_LABEL_KEY)
        String labelJson,
        @JsonView(AbstractBaseBO.EditView.class) @RequestBody() @Valid KnowledgeFileParseTaskDO bean);

    @PostMapping
    @Operation(summary = "新增-notNotify")
    @JsonView(AbstractBaseBO.ShowView.class)
    @Validated(AbstractBaseBO.CreateAction.class)
    public ResultDTO<KnowledgeFileParseTaskDO> add(
		@Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
		@Parameter(description = "请求用户账号", example = "admin") @RequestHeader(required = true, value = Constants.HEADER_ACCOUNT_KEY) @NotBlank  String userAccount,
    @Parameter(description = "是否不发送通知，true：不发送，false：发送", example = "false") @RequestParam(required = false,  value = "notNotify") boolean notNotify,
		@JsonView(AbstractBaseBO.EditView.class) @RequestBody() @Valid KnowledgeFileParseTaskDO bean);

    @PostMapping
    @Operation(summary = "新增-notNotify-notNotifyToSelf")
    @JsonView(AbstractBaseBO.ShowView.class)
    @Validated(AbstractBaseBO.CreateAction.class)
    public ResultDTO<KnowledgeFileParseTaskDO> add(
		@Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
		@Parameter(description = "请求用户账号", example = "admin") @RequestHeader(required = true, value = Constants.HEADER_ACCOUNT_KEY) @NotBlank  String userAccount,
    @Parameter(description = "是否不发送通知，true：不发送，false：发送", example = "false") @RequestParam(required = false,  value = "notNotify") boolean notNotify,
    @Parameter(description = "是否发送变更通知给自己，true: 不发送，false: 发送", example = "false") @RequestParam(required = false,  value = "notNotifyToSelf") boolean notNotifyToSelf,
		@JsonView(AbstractBaseBO.EditView.class) @RequestBody() @Valid KnowledgeFileParseTaskDO bean);

    @PostMapping
    @Operation(summary = "新增-notNotify")
    @JsonView(AbstractBaseBO.ShowView.class)
    @Validated(AbstractBaseBO.CreateAction.class)
    public ResultDTO<KnowledgeFileParseTaskDO> add(
        @Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
         @Parameter(description = "数据标签")
         @RequestHeader(required = false, value = Constants.HEADER_DATA_TAG_KEY)
         List<String> dataTag,
         @Parameter(description = "领域标签")
         @RequestHeader(required = false, value = Constants.HEADER_DOMAIN_LABEL_KEY)
         String labelJson,
        @Parameter(description = "请求用户账号", example = "admin") @RequestHeader(required = true, value = Constants.HEADER_ACCOUNT_KEY) @NotBlank  String userAccount,
        @Parameter(description = "是否不发送通知，true：不发送，false：发送", example = "false") @RequestParam(required = false,  value = "notNotify") boolean notNotify,
        @JsonView(AbstractBaseBO.EditView.class) @RequestBody() @Valid KnowledgeFileParseTaskDO bean);

        @PostMapping
    @Operation(summary = "新增-notNotify-notNotifyToSelf")
    @JsonView(AbstractBaseBO.ShowView.class)
    @Validated(AbstractBaseBO.CreateAction.class)
    public ResultDTO<KnowledgeFileParseTaskDO> add(
        @Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
         @Parameter(description = "数据标签")
         @RequestHeader(required = false, value = Constants.HEADER_DATA_TAG_KEY)
         List<String> dataTag,
         @Parameter(description = "领域标签")
         @RequestHeader(required = false, value = Constants.HEADER_DOMAIN_LABEL_KEY)
         String labelJson,
        @Parameter(description = "请求用户账号", example = "admin") @RequestHeader(required = true, value = Constants.HEADER_ACCOUNT_KEY) @NotBlank  String userAccount,
        @Parameter(description = "是否不发送通知，true：不发送，false：发送", example = "false") @RequestParam(required = false,  value = "notNotify") boolean notNotify,
        @Parameter(description = "是否发送变更通知给自己，true: 不发送，false: 发送", example = "false") @RequestParam(required = false,  value = "notNotifyToSelf") boolean notNotifyToSelf,
        @JsonView(AbstractBaseBO.EditView.class) @RequestBody() @Valid KnowledgeFileParseTaskDO bean);

    @PostMapping
    @Operation(summary = "新增-notifyParams")
    @JsonView(AbstractBaseBO.ShowView.class)
    @Validated(AbstractBaseBO.CreateAction.class)
    public ResultDTO<KnowledgeFileParseTaskDO> add(
        @Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
         @Parameter(description = "数据标签")
         @RequestHeader(required = false, value = Constants.HEADER_DATA_TAG_KEY)
         List<String> dataTag,
         @Parameter(description = "领域标签")
         @RequestHeader(required = false, value = Constants.HEADER_DOMAIN_LABEL_KEY)
         String labelJson,
        @Parameter(description = "请求用户账号", example = "admin") @RequestHeader(required = true, value = Constants.HEADER_ACCOUNT_KEY) @NotBlank  String userAccount,
        @SpringQueryMap NotifyParams notifyParams,@JsonView(AbstractBaseBO.EditView.class) @RequestBody() @Valid KnowledgeFileParseTaskDO bean);

    @PostMapping("/all")
    @Operation(summary = "批量新增")
    @JsonView(AbstractBaseBO.ShowView.class)
    @Validated(AbstractBaseBO.CreateAction.class)
    public ResultDTO<Iterable<KnowledgeFileParseTaskDO>> addAll(
		@Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
		@Parameter(description = "请求用户账号", example = "admin") @RequestHeader(required = true, value = Constants.HEADER_ACCOUNT_KEY) @NotBlank  String userAccount,
		@RequestBody() @JsonView(AbstractBaseBO.EditView.class) @Valid ValidatedList<KnowledgeFileParseTaskDO> beans);

    @PostMapping("/all")
    @Operation(summary = "批量新增")
    @JsonView(AbstractBaseBO.ShowView.class)
    @Validated(AbstractBaseBO.CreateAction.class)
    public ResultDTO<Iterable<KnowledgeFileParseTaskDO>> addAll(
        @Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
        @Parameter(description = "数据标签")
        @RequestHeader(required = false, value = Constants.HEADER_DATA_TAG_KEY)
        List<String> dataTag,
        @Parameter(description = "领域标签")
        @RequestHeader(required = false, value = Constants.HEADER_DOMAIN_LABEL_KEY)
        String labelJson,
        @Parameter(description = "请求用户账号", example = "admin") @RequestHeader(required = true, value = Constants.HEADER_ACCOUNT_KEY) @NotBlank  String userAccount,
        @RequestBody() @JsonView(AbstractBaseBO.EditView.class) @Valid ValidatedList<KnowledgeFileParseTaskDO> beans);

    @PostMapping("/all")
    @Operation(summary = "批量新增-notNotify")
    @JsonView(AbstractBaseBO.ShowView.class)
    @Validated(AbstractBaseBO.CreateAction.class)
    public ResultDTO<Iterable<KnowledgeFileParseTaskDO>> addAll(
		@Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
		@Parameter(description = "请求用户账号", example = "admin") @RequestHeader(required = true, value = Constants.HEADER_ACCOUNT_KEY) @NotBlank  String userAccount,
    @Parameter(description = "是否不发送通知，true：不发送，false：发送", example = "false") @RequestParam(required = false,  value = "notNotify") boolean notNotify,
		@RequestBody() @JsonView(AbstractBaseBO.EditView.class) @Valid ValidatedList<KnowledgeFileParseTaskDO> beans);

    @PostMapping("/all")
    @Operation(summary = "批量新增-notNotify-notNotifyToSelf")
    @JsonView(AbstractBaseBO.ShowView.class)
    @Validated(AbstractBaseBO.CreateAction.class)
    public ResultDTO<Iterable<KnowledgeFileParseTaskDO>> addAll(
		@Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
		@Parameter(description = "请求用户账号", example = "admin") @RequestHeader(required = true, value = Constants.HEADER_ACCOUNT_KEY) @NotBlank  String userAccount,
    @Parameter(description = "是否不发送通知，true：不发送，false：发送", example = "false") @RequestParam(required = false,  value = "notNotify") boolean notNotify,
    @Parameter(description = "是否发送变更通知给自己，true: 不发送，false: 发送", example = "false") @RequestParam(required = false,  value = "notNotifyToSelf") boolean notNotifyToSelf,
		@RequestBody() @JsonView(AbstractBaseBO.EditView.class) @Valid ValidatedList<KnowledgeFileParseTaskDO> beans);

    @PostMapping(value = "/all")
    @Operation(summary = "批量新增-notNotify")
    @JsonView(AbstractBaseBO.ShowView.class)
    @Validated(AbstractBaseBO.CreateAction.class)
    public ResultDTO<Iterable<KnowledgeFileParseTaskDO>> addAll(
        @Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
        @Parameter(description = "数据标签")
        @RequestHeader(required = false, value = Constants.HEADER_DATA_TAG_KEY)
        List<String> dataTag,
        @Parameter(description = "领域标签")
        @RequestHeader(required = false, value = Constants.HEADER_DOMAIN_LABEL_KEY)
        String labelJson,
        @Parameter(description = "请求用户账号", example = "admin") @RequestHeader(required = true, value = Constants.HEADER_ACCOUNT_KEY) @NotBlank  String userAccount,
    	@Parameter(description = "是否不发送通知，true：不发送，false：发送", example = "false") @RequestParam(required = false,  value = "notNotify") boolean notNotify,
        @RequestBody() @JsonView(AbstractBaseBO.EditView.class) @Valid ValidatedList<KnowledgeFileParseTaskDO> beans);
    
    @PostMapping(value = "/all")
    @Operation(summary = "批量新增-notNotify-notNotifyToSelf")
    @JsonView(AbstractBaseBO.ShowView.class)
    @Validated(AbstractBaseBO.CreateAction.class)
    public ResultDTO<Iterable<KnowledgeFileParseTaskDO>> addAll(
        @Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
        @Parameter(description = "数据标签")
        @RequestHeader(required = false, value = Constants.HEADER_DATA_TAG_KEY)
        List<String> dataTag,
        @Parameter(description = "领域标签")
        @RequestHeader(required = false, value = Constants.HEADER_DOMAIN_LABEL_KEY)
        String labelJson,
        @Parameter(description = "请求用户账号", example = "admin") @RequestHeader(required = true, value = Constants.HEADER_ACCOUNT_KEY) @NotBlank  String userAccount,
    	@Parameter(description = "是否不发送通知，true：不发送，false：发送", example = "false") @RequestParam(required = false,  value = "notNotify") boolean notNotify,
      @Parameter(description = "是否发送变更通知给自己，true: 不发送，false: 发送", example = "false") @RequestParam(required = false,  value = "notNotifyToSelf") boolean notNotifyToSelf,
        @RequestBody() @JsonView(AbstractBaseBO.EditView.class) @Valid ValidatedList<KnowledgeFileParseTaskDO> beans);
    
    @PostMapping(value = "/all")
    @Operation(summary = "批量新增-notifyParams")
    @JsonView(AbstractBaseBO.ShowView.class)
    @Validated(AbstractBaseBO.CreateAction.class)
    public ResultDTO<Iterable<KnowledgeFileParseTaskDO>> addAll(
        @Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
        @Parameter(description = "数据标签")
        @RequestHeader(required = false, value = Constants.HEADER_DATA_TAG_KEY)
        List<String> dataTag,
        @Parameter(description = "领域标签")
        @RequestHeader(required = false, value = Constants.HEADER_DOMAIN_LABEL_KEY)
        String labelJson,
        @Parameter(description = "请求用户账号", example = "admin") @RequestHeader(required = true, value = Constants.HEADER_ACCOUNT_KEY) @NotBlank  String userAccount,
    	  @SpringQueryMap NotifyParams notifyParams,@RequestBody() @JsonView(AbstractBaseBO.EditView.class) @Valid ValidatedList<KnowledgeFileParseTaskDO> beans);

	  @PostMapping(value = "/all?returnRes=false")
    @Operation(summary = "批量新增-notNotify")
    @JsonView(AbstractBaseBO.ShowView.class)
    @Validated(AbstractBaseBO.CreateAction.class)
    public ResultDTO<Iterable<String>> addAllIDs(
        @Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
        @Parameter(description = "数据标签")
        @RequestHeader(required = false, value = Constants.HEADER_DATA_TAG_KEY)
        List<String> dataTag,
        @Parameter(description = "领域标签")
        @RequestHeader(required = false, value = Constants.HEADER_DOMAIN_LABEL_KEY)
        String labelJson,
        @Parameter(description = "请求用户账号", example = "admin") @RequestHeader(required = true, value = Constants.HEADER_ACCOUNT_KEY) @NotBlank  String userAccount,
		@Parameter(description = "是否不发送通知，true：不发送，false：发送", example = "false") @RequestParam(required = false,  value = "notNotify") boolean notNotify,
        @RequestBody() @JsonView(AbstractBaseBO.EditView.class) @Valid ValidatedList<KnowledgeFileParseTaskDO> beans);

    @PostMapping(value = "/all?returnRes=false")
    @Operation(summary = "批量新增-notNotify-notNotifyToSelf")
    @JsonView(AbstractBaseBO.ShowView.class)
    @Validated(AbstractBaseBO.CreateAction.class)
    public ResultDTO<Iterable<String>> addAllIDs(
        @Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
        @Parameter(description = "数据标签")
        @RequestHeader(required = false, value = Constants.HEADER_DATA_TAG_KEY)
        List<String> dataTag,
        @Parameter(description = "领域标签")
        @RequestHeader(required = false, value = Constants.HEADER_DOMAIN_LABEL_KEY)
        String labelJson,
        @Parameter(description = "请求用户账号", example = "admin") @RequestHeader(required = true, value = Constants.HEADER_ACCOUNT_KEY) @NotBlank  String userAccount,
		@Parameter(description = "是否不发送通知，true：不发送，false：发送", example = "false") @RequestParam(required = false,  value = "notNotify") boolean notNotify,
    @Parameter(description = "是否发送变更通知给自己，true: 不发送，false: 发送", example = "false")  @RequestParam(required = false,  value = "notNotifyToSelf") boolean notNotifyToSelf,
        @RequestBody() @JsonView(AbstractBaseBO.EditView.class) @Valid ValidatedList<KnowledgeFileParseTaskDO> beans);

    @PostMapping(value = "/all?returnRes=false")
    @Operation(summary = "批量新增-notifyParams")
    @JsonView(AbstractBaseBO.ShowView.class)
    @Validated(AbstractBaseBO.CreateAction.class)
    public ResultDTO<Iterable<String>> addAllIDs(
        @Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
        @Parameter(description = "数据标签")
        @RequestHeader(required = false, value = Constants.HEADER_DATA_TAG_KEY)
        List<String> dataTag,
        @Parameter(description = "领域标签")
        @RequestHeader(required = false, value = Constants.HEADER_DOMAIN_LABEL_KEY)
        String labelJson,
        @Parameter(description = "请求用户账号", example = "admin") @RequestHeader(required = true, value = Constants.HEADER_ACCOUNT_KEY) @NotBlank  String userAccount,
		    @SpringQueryMap NotifyParams notifyParams,@RequestBody() @JsonView(AbstractBaseBO.EditView.class) @Valid ValidatedList<KnowledgeFileParseTaskDO> beans);

    @DeleteMapping("/{id}")
    @Operation(summary = "删除")
    @JsonView(AbstractBaseBO.EditView.class)
    public ResultDTO<KnowledgeFileParseTaskDO> delete(
		@Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
		@Parameter(required = true, description = "删除对象ID", example = "admin") @PathVariable(value = "id") String id);

    @DeleteMapping("/{id}")
    @Operation(summary = "删除-notNotify")
    @JsonView(AbstractBaseBO.EditView.class)
    public ResultDTO<KnowledgeFileParseTaskDO> delete(
		@Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
    @Parameter(description = "是否不发送通知，true：不发送，false：发送", example = "false") @RequestParam(required = false,  value = "notNotify") boolean notNotify,
		@Parameter(required = true, description = "删除对象ID", example = "admin") @PathVariable(value = "id") String id);

    @DeleteMapping("/{id}")
    @Operation(summary = "删除-notNotify-notNotifyToSelf")
    @JsonView(AbstractBaseBO.EditView.class)
    public ResultDTO<KnowledgeFileParseTaskDO> delete(
		@Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
    @Parameter(description = "是否不发送通知，true：不发送，false：发送", example = "false") @RequestParam(required = false,  value = "notNotify") boolean notNotify,
    @Parameter(description = "是否发送变更通知给自己，true: 不发送，false: 发送", example = "false") @RequestParam(required = false,  value = "notNotifyToSelf") boolean notNotifyToSelf,
		@Parameter(required = true, description = "删除对象ID", example = "admin") @PathVariable(value = "id") String id);

    @DeleteMapping("/{id}")
    @Operation(summary = "删除-notifyParams")
    @JsonView(AbstractBaseBO.EditView.class)
    public ResultDTO<KnowledgeFileParseTaskDO> delete(
		@Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
    @SpringQueryMap NotifyParams notifyParams,@Parameter(required = true, description = "删除对象ID", example = "admin") @PathVariable(value = "id") String id);

    @DeleteMapping("/all")
    @Operation(summary = "删除")
    @JsonView(AbstractBaseBO.EditView.class)
    public ResultDTO<List<KnowledgeFileParseTaskDO>> deleteAll(
		@Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
		@Parameter(required = true, description = "删除对象ID列表") @RequestParam(value = "ids") List<String> ids);

    @DeleteMapping("/all")
    @Operation(summary = "删除-notNotify")
    @JsonView(AbstractBaseBO.EditView.class)
    public ResultDTO<List<KnowledgeFileParseTaskDO>> deleteAll(
		@Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
    @Parameter(description = "是否不发送通知，true：不发送，false：发送", example = "false") @RequestParam(required = false,  value = "notNotify") boolean notNotify,
		@Parameter(required = true, description = "删除对象ID列表") @RequestParam(value = "ids") List<String> ids);

    @DeleteMapping("/all")
    @Operation(summary = "删除-notNotify-notNotifyToSelf")
    @JsonView(AbstractBaseBO.EditView.class)
    public ResultDTO<List<KnowledgeFileParseTaskDO>> deleteAll(
		@Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
    @Parameter(description = "是否不发送通知，true：不发送，false：发送", example = "false") @RequestParam(required = false,  value = "notNotify") boolean notNotify,
    @Parameter(description = "是否发送变更通知给自己，true: 不发送，false: 发送", example = "false") @RequestParam(required = false,  value = "notNotifyToSelf") boolean notNotifyToSelf,
		@Parameter(required = true, description = "删除对象ID列表") @RequestParam(value = "ids") List<String> ids);

    @DeleteMapping("/all")
    @Operation(summary = "删除-notifyParams")
    @JsonView(AbstractBaseBO.EditView.class)
    public ResultDTO<List<KnowledgeFileParseTaskDO>> deleteAll(
		@Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
    @SpringQueryMap NotifyParams notifyParams,@Parameter(required = true, description = "删除对象ID列表") @RequestParam(value = "ids") List<String> ids);

    @DeleteMapping("/query")
    @JsonView(ShowView.class)
    @Operation(summary = "自定义条件删除")
    public ResultDTO<List<KnowledgeFileParseTaskDO>> deleteByQuery(
        @Parameter(description = "租户ID", example = "tenant-A")
        @RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
        String tenantID,
        @Parameter(description = "命名空间", example = "com.polarizon")
        @RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
        String namespace,
        @Parameter(description = "自定义mongo查询json")
        @RequestParam(required = false, value = "queryJson")
        String queryJson
    );

    @GetMapping("/{id}")
    @Operation(summary = "查询ID")
    @JsonView(ShowView.class)
    public ResultDTO<KnowledgeFileParseTaskDO> findOneByID(
		@Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
		@Parameter(required = true, description = "对象ID", example = "admin") @PathVariable(required = true, value = "id") String id);

    @GetMapping("/all")
    @Operation(summary = "批量查询")
    @JsonView(ShowView.class)
    public ResultDTO<List<KnowledgeFileParseTaskDO>> listAll(
		@Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
		@Parameter(description = "查找对象ID列表") @RequestParam(required = false, value = "ids") List<String> ids);

    @GetMapping("/all/page")
    @Operation(summary = "分页查询")
    @JsonView(ShowView.class)
    public ResultDTO<Page<KnowledgeFileParseTaskDO>> listPage(
		@Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
		@Parameter(description = "查找对象ID列表") @RequestParam(required = false, value = "ids") List<String> ids,
		Pageable pageable);

    @PutMapping
    @Operation(summary = "单独修改，幂等操作，需要传入ID")
    @JsonView(ShowView.class)
    @Validated(UpdateAction.class)
    public ResultDTO<KnowledgeFileParseTaskDO> update(
		@Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
		@Parameter(description = "请求用户账号", example = "admin") @RequestHeader(required = true, value = Constants.HEADER_ACCOUNT_KEY) @NotBlank  String userAccount,
		@RequestBody() @JsonView(EditView.class) @Valid KnowledgeFileParseTaskDO source);

    @PutMapping
    @Operation(summary = "单独修改，幂等操作，需要传入ID-notNotify")
    @JsonView(ShowView.class)
    @Validated(UpdateAction.class)
    public ResultDTO<KnowledgeFileParseTaskDO> update(
		@Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
		@Parameter(description = "请求用户账号", example = "admin") @RequestHeader(required = true, value = Constants.HEADER_ACCOUNT_KEY) @NotBlank  String userAccount,
    @Parameter(description = "是否不发送通知，true：不发送，false：发送", example = "false") @RequestParam(required = false,  value = "notNotify") boolean notNotify,
		@RequestBody() @JsonView(EditView.class) @Valid KnowledgeFileParseTaskDO source);

    @PutMapping
    @Operation(summary = "单独修改，幂等操作，需要传入ID-notNotify-notNotifyToSelf")
    @JsonView(ShowView.class)
    @Validated(UpdateAction.class)
    public ResultDTO<KnowledgeFileParseTaskDO> update(
		@Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
		@Parameter(description = "请求用户账号", example = "admin") @RequestHeader(required = true, value = Constants.HEADER_ACCOUNT_KEY) @NotBlank  String userAccount,
    @Parameter(description = "是否不发送通知，true：不发送，false：发送", example = "false") @RequestParam(required = false,  value = "notNotify") boolean notNotify,
    @Parameter(description = "是否发送变更通知给自己，true: 不发送，false: 发送", example = "false") @RequestParam(required = false,  value = "notNotifyToSelf") boolean notNotifyToSelf,
		@RequestBody() @JsonView(EditView.class) @Valid KnowledgeFileParseTaskDO source);

    @PutMapping
    @Operation(summary = "单独修改，幂等操作，需要传入ID-notifyParams")
    @JsonView(ShowView.class)
    @Validated(UpdateAction.class)
    public ResultDTO<KnowledgeFileParseTaskDO> update(
		@Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
		@Parameter(description = "请求用户账号", example = "admin") @RequestHeader(required = true, value = Constants.HEADER_ACCOUNT_KEY) @NotBlank  String userAccount,
    @SpringQueryMap NotifyParams notifyParams,@RequestBody() @JsonView(EditView.class) @Valid KnowledgeFileParseTaskDO source);

    @PutMapping("/all")
    @Operation(summary = "批量修改，幂等操作，需要传入ID")
    @JsonView(ShowView.class)
    @Validated(UpdateAction.class)
    public ResultDTO<Iterable<KnowledgeFileParseTaskDO>> updateAll(
		@Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
		@Parameter(description = "请求用户账号", example = "admin") @RequestHeader(required = true, value = Constants.HEADER_ACCOUNT_KEY) @NotBlank  String userAccount,
		@Parameter(description = "记录是否必须存在", required = false) @RequestParam(value = "prevExist", required = false) boolean prevExist,
		@RequestBody() @JsonView(EditView.class) @Valid ValidatedList<KnowledgeFileParseTaskDO> beans);

    @PutMapping("/all")
    @Operation(summary = "批量修改，幂等操作，需要传入ID-notNotify")
    @JsonView(ShowView.class)
    @Validated(UpdateAction.class)
    public ResultDTO<Iterable<KnowledgeFileParseTaskDO>> updateAll(
		@Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
		@Parameter(description = "请求用户账号", example = "admin") @RequestHeader(required = true, value = Constants.HEADER_ACCOUNT_KEY) @NotBlank  String userAccount,
		@Parameter(description = "记录是否必须存在", required = false) @RequestParam(value = "prevExist", required = false) boolean prevExist,
    @Parameter(description = "是否不发送通知，true：不发送，false：发送", example = "false") @RequestParam(required = false,  value = "notNotify") boolean notNotify,
		@RequestBody() @JsonView(EditView.class) @Valid ValidatedList<KnowledgeFileParseTaskDO> beans);

    @PutMapping("/all")
    @Operation(summary = "批量修改，幂等操作，需要传入ID-notNotify-notNotifyToSelf")
    @JsonView(ShowView.class)
    @Validated(UpdateAction.class)
    public ResultDTO<Iterable<KnowledgeFileParseTaskDO>> updateAll(
		@Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
		@Parameter(description = "请求用户账号", example = "admin") @RequestHeader(required = true, value = Constants.HEADER_ACCOUNT_KEY) @NotBlank  String userAccount,
		@Parameter(description = "记录是否必须存在", required = false) @RequestParam(value = "prevExist", required = false) boolean prevExist,
    @Parameter(description = "是否不发送通知，true：不发送，false：发送", example = "false") @RequestParam(required = false,  value = "notNotify") boolean notNotify,
    @Parameter(description = "是否发送变更通知给自己，true: 不发送，false: 发送", example = "false") @RequestParam(required = false,  value = "notNotifyToSelf") boolean notNotifyToSelf,
		@RequestBody() @JsonView(EditView.class) @Valid ValidatedList<KnowledgeFileParseTaskDO> beans);

    @PutMapping("/all")
    @Operation(summary = "批量修改，幂等操作，需要传入ID-notifyParams")
    @JsonView(ShowView.class)
    @Validated(UpdateAction.class)
    public ResultDTO<Iterable<KnowledgeFileParseTaskDO>> updateAll(
		@Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
		@Parameter(description = "请求用户账号", example = "admin") @RequestHeader(required = true, value = Constants.HEADER_ACCOUNT_KEY) @NotBlank  String userAccount,
		@Parameter(description = "记录是否必须存在", required = false) @RequestParam(value = "prevExist", required = false) boolean prevExist,
    @SpringQueryMap NotifyParams notifyParams,@RequestBody() @JsonView(EditView.class) @Valid ValidatedList<KnowledgeFileParseTaskDO> beans);
    
    @PatchMapping
    @Operation(summary = "单独更新，非幂等操作，传入ID则修改，无ID则新建")
    @JsonView(ShowView.class)
    public ResultDTO<KnowledgeFileParseTaskDO> save(
		@Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
		@Parameter(description = "请求用户账号", example = "admin") @RequestHeader(required = true, value = Constants.HEADER_ACCOUNT_KEY) @NotBlank  String userAccount,
		@RequestBody() @JsonView(EditView.class) @Valid KnowledgeFileParseTaskDO source);

    @PatchMapping
    @Operation(summary = "单独更新，非幂等操作，传入ID则修改，无ID则新建-notNotify")
    @JsonView(ShowView.class)
    public ResultDTO<KnowledgeFileParseTaskDO> save(
		@Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
		@Parameter(description = "请求用户账号", example = "admin") @RequestHeader(required = true, value = Constants.HEADER_ACCOUNT_KEY) @NotBlank  String userAccount,
    @Parameter(description = "是否不发送通知，true：不发送，false：发送", example = "false") @RequestParam(required = false,  value = "notNotify") boolean notNotify,
		@RequestBody() @JsonView(EditView.class) @Valid KnowledgeFileParseTaskDO source);

    @PatchMapping
    @Operation(summary = "单独更新，非幂等操作，传入ID则修改，无ID则新建-notNotify-notNotifyToSelf")
    @JsonView(ShowView.class)
    public ResultDTO<KnowledgeFileParseTaskDO> save(
		@Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
		@Parameter(description = "请求用户账号", example = "admin") @RequestHeader(required = true, value = Constants.HEADER_ACCOUNT_KEY) @NotBlank  String userAccount,
    @Parameter(description = "是否不发送通知，true：不发送，false：发送", example = "false") @RequestParam(required = false,  value = "notNotify") boolean notNotify,
    @Parameter(description = "是否发送变更通知给自己，true: 不发送，false: 发送", example = "false") @RequestParam(required = false,  value = "notNotifyToSelf") boolean notNotifyToSelf,
		@RequestBody() @JsonView(EditView.class) @Valid KnowledgeFileParseTaskDO source);

    @PatchMapping
    @Operation(summary = "单独更新，非幂等操作，传入ID则修改，无ID则新建-notifyParams")
    @JsonView(ShowView.class)
    public ResultDTO<KnowledgeFileParseTaskDO> save(
		@Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
		@Parameter(description = "请求用户账号", example = "admin") @RequestHeader(required = true, value = Constants.HEADER_ACCOUNT_KEY) @NotBlank  String userAccount,
    @SpringQueryMap NotifyParams notifyParams,@RequestBody() @JsonView(EditView.class) @Valid KnowledgeFileParseTaskDO source);

    @PatchMapping("/all")
    @Operation(summary = "批量更新，非幂等操作，传入ID则修改，无ID则新建")
    @JsonView(ShowView.class)
    public ResultDTO<Iterable<KnowledgeFileParseTaskDO>> saveAll(
		@Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
		@Parameter(description = "请求用户账号", example = "admin") @RequestHeader(required = true, value = Constants.HEADER_ACCOUNT_KEY) @NotBlank  String userAccount,
		@Parameter(description = "记录是否必须存在", required = false) @RequestParam(value = "prevExist", required = false) boolean prevExist,
		@RequestBody() @JsonView(EditView.class) @Valid ValidatedList<KnowledgeFileParseTaskDO> beans);

    @PatchMapping("/all")
    @Operation(summary = "批量更新，非幂等操作，传入ID则修改，无ID则新建-notNotify")
    @JsonView(ShowView.class)
    public ResultDTO<Iterable<KnowledgeFileParseTaskDO>> saveAll(
		@Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
		@Parameter(description = "请求用户账号", example = "admin") @RequestHeader(required = true, value = Constants.HEADER_ACCOUNT_KEY) @NotBlank  String userAccount,
		@Parameter(description = "记录是否必须存在", required = false) @RequestParam(value = "prevExist", required = false) boolean prevExist,
    @Parameter(description = "是否不发送通知，true：不发送，false：发送", example = "false") @RequestParam(required = false,  value = "notNotify") boolean notNotify,
		@RequestBody() @JsonView(EditView.class) @Valid ValidatedList<KnowledgeFileParseTaskDO> beans);

    @PatchMapping("/all")
    @Operation(summary = "批量更新，非幂等操作，传入ID则修改，无ID则新建-notNotify-notNotifyToSelf")
    @JsonView(ShowView.class)
    public ResultDTO<Iterable<KnowledgeFileParseTaskDO>> saveAll(
		@Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
		@Parameter(description = "请求用户账号", example = "admin") @RequestHeader(required = true, value = Constants.HEADER_ACCOUNT_KEY) @NotBlank  String userAccount,
		@Parameter(description = "记录是否必须存在", required = false) @RequestParam(value = "prevExist", required = false) boolean prevExist,
    @Parameter(description = "是否不发送通知，true：不发送，false：发送", example = "false") @RequestParam(required = false,  value = "notNotify") boolean notNotify,
    @Parameter(description = "是否发送变更通知给自己，true: 不发送，false: 发送", example = "false")  @RequestParam(required = false,  value = "notNotifyToSelf") boolean notNotifyToSelf,
		@RequestBody() @JsonView(EditView.class) @Valid ValidatedList<KnowledgeFileParseTaskDO> beans);

    @PatchMapping("/all")
    @Operation(summary = "批量更新，非幂等操作，传入ID则修改，无ID则新建-notifyParams")
    @JsonView(ShowView.class)
    public ResultDTO<Iterable<KnowledgeFileParseTaskDO>> saveAll(
		@Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
		@Parameter(description = "请求用户账号", example = "admin") @RequestHeader(required = true, value = Constants.HEADER_ACCOUNT_KEY) @NotBlank  String userAccount,
		@Parameter(description = "记录是否必须存在", required = false) @RequestParam(value = "prevExist", required = false) boolean prevExist,
    @SpringQueryMap NotifyParams notifyParams,@RequestBody() @JsonView(EditView.class) @Valid ValidatedList<KnowledgeFileParseTaskDO> beans);
    
    @PutMapping("/all")
    @Operation(summary = "批量修改，幂等操作，需要传入ID-notNotify")
    @JsonView(ShowView.class)
    @Validated(UpdateAction.class)
    public ResultDTO<Iterable<KnowledgeFileParseTaskDO>> updateAll(
    @Parameter(description = "租户ID", example = "tenant-A")
    @RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
    String tenantID,
    @Parameter(description = "命名空间", example = "com.polarizon")
    @RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
    String namespace,
    @Parameter(description = "请求用户账号", example = "admin") @RequestHeader(required = true, value = Constants.HEADER_ACCOUNT_KEY) @NotBlank  String userAccount,
    @Parameter(description = "记录是否必须存在", required = false) @RequestParam(value = "prevExist", required = false) boolean prevExist,
    @Parameter(description = "是否不发送通知，true：不发送，false：发送", example = "false") @RequestParam(required = false,  value = "notNotify") boolean notNotify,
    @Parameter(description = "空字段数据是否更新，true：更新，false：不更新(不校验以前是否存在)", required = false) @RequestParam(value = "updateNull", required = false, defaultValue = "false") boolean updateNull,
    @Parameter(description = "返回更新结果", required = false) @RequestParam(value = "returnRes", required = false, defaultValue = "true") boolean returnRes,
    @RequestBody() @JsonView(EditView.class) @Valid ValidatedList<KnowledgeFileParseTaskDO> beans);

    @PutMapping("/all")
    @Operation(summary = "批量修改，幂等操作，需要传入ID-notNotify-notNotifyToSelf")
    @JsonView(ShowView.class)
    @Validated(UpdateAction.class)
    public ResultDTO<Iterable<KnowledgeFileParseTaskDO>> updateAll(
    @Parameter(description = "租户ID", example = "tenant-A")
    @RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
    String tenantID,
    @Parameter(description = "命名空间", example = "com.polarizon")
    @RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
    String namespace,
    @Parameter(description = "请求用户账号", example = "admin") @RequestHeader(required = true, value = Constants.HEADER_ACCOUNT_KEY) @NotBlank  String userAccount,
    @Parameter(description = "记录是否必须存在", required = false) @RequestParam(value = "prevExist", required = false) boolean prevExist,
    @Parameter(description = "是否不发送通知，true：不发送，false：发送", example = "false") @RequestParam(required = false,  value = "notNotify") boolean notNotify,
    @Parameter(description = "是否发送变更通知给自己，true: 不发送，false: 发送", example = "false") @RequestParam(required = false,  value = "notNotifyToSelf") boolean notNotifyToSelf,
    @Parameter(description = "空字段数据是否更新，true：更新，false：不更新(不校验以前是否存在)", required = false) @RequestParam(value = "updateNull", required = false, defaultValue = "false") boolean updateNull,
    @Parameter(description = "返回更新结果", required = false) @RequestParam(value = "returnRes", required = false, defaultValue = "true") boolean returnRes,
    @RequestBody() @JsonView(EditView.class) @Valid ValidatedList<KnowledgeFileParseTaskDO> beans);

    @PutMapping("/all")
    @Operation(summary = "批量修改，幂等操作，需要传入ID-notifyParams")
    @JsonView(ShowView.class)
    @Validated(UpdateAction.class)
    public ResultDTO<Iterable<KnowledgeFileParseTaskDO>> updateAll(
    @Parameter(description = "租户ID", example = "tenant-A")
    @RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
    String tenantID,
    @Parameter(description = "命名空间", example = "com.polarizon")
    @RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
    String namespace,
    @Parameter(description = "请求用户账号", example = "admin") @RequestHeader(required = true, value = Constants.HEADER_ACCOUNT_KEY) @NotBlank  String userAccount,
    @Parameter(description = "记录是否必须存在", required = false) @RequestParam(value = "prevExist", required = false) boolean prevExist,
    @SpringQueryMap NotifyParams notifyParams,
    @Parameter(description = "空字段数据是否更新，true：更新，false：不更新(不校验以前是否存在)", required = false) @RequestParam(value = "updateNull", required = false, defaultValue = "false") boolean updateNull,
    @Parameter(description = "返回更新结果", required = false) @RequestParam(value = "returnRes", required = false, defaultValue = "true") boolean returnRes,
    @RequestBody() @JsonView(EditView.class) @Valid ValidatedList<KnowledgeFileParseTaskDO> beans);

	@GetMapping("/admin")
	@JsonView(ShowView.class)
	@Operation(summary = "admin查询")
	public ResultDTO<List<KnowledgeFileParseTaskDO>> admin(
		@Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
		@SpringQueryMap KnowledgeFileParseTaskDOParams params,
		@Parameter(description = "只包含命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = "includeNamespace")
		List<String> includeNamespace,
		@Parameter(description = "排除的命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = "excludeNamespace")
		List<String> excludeNamespace
	);

	  @GetMapping("/query")
    @JsonView(ShowView.class)
    @Operation(summary = "自定义条件查询")
    public ResultDTO<List<KnowledgeFileParseTaskDO>> query(
		@Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
        @Parameter(description = "自定义mongo查询json")
        @RequestParam(required = false, value = "queryJson")
        String queryJson
    );

    @GetMapping("/query/limit")
    @JsonView(ShowView.class)
    @Operation(summary = "自定义条件查询")
    public ResultDTO<List<KnowledgeFileParseTaskDO>> queryLimit(
		@Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
        @Parameter(description = "自定义mongo查询json")
        @RequestParam(required = false, value = "queryJson")
        String queryJson,
        Pageable pageable
    );

    @PostMapping("/query/aggregation")
    @JsonView(ShowView.class)
    @Operation(summary = "自定义聚合查询")
    public ResultDTO<List<Map>> query(
		@Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
        @RequestBody List<String> operations
    );

    @GetMapping("/query/page")
    @JsonView(ShowView.class)
    @Operation(summary = "自定义条件查询")
    public ResultDTO<Page<KnowledgeFileParseTaskDO>> queryPage(
		@Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
        @Parameter(description = "自定义mongo查询json")
        @RequestParam(required = false, value = "queryJson")
        String queryJson,
        Pageable pageable
	);
        

	@GetMapping("/admin/page")
	@JsonView(ShowView.class)
	@Operation(summary = "admin分页查询")
	public ResultDTO<Page<KnowledgeFileParseTaskDO>> adminPage(
		@Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
		@SpringQueryMap KnowledgeFileParseTaskDOParams params,
		@Parameter(description = "只包含命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = "includeNamespace")
		List<String> includeNamespace,
		@Parameter(description = "排除的命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = "excludeNamespace")
		List<String> excludeNamespace,
		Pageable pageable
	);

	@PostMapping("/conditions")
	@JsonView(ShowView.class)
	@Operation(summary = "通用条件查询")
	public ResultDTO<List<KnowledgeFileParseTaskDO>> conditions(
		@RequestBody KnowledgeFileParseTaskDOParams params
	);

	@PostMapping("/conditions")
	@JsonView(ShowView.class)
	@Operation(summary = "通用条件查询")
	public ResultDTO<List<KnowledgeFileParseTaskDO>> conditions(
		@Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
		@RequestBody KnowledgeFileParseTaskDOParams params
	);

	@PostMapping("/conditions")
    @JsonView(ShowView.class)
    @Operation(summary = "通用条件查询")
    public ResultDTO<List<KnowledgeFileParseTaskDO>> conditions(
        @Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
        @Parameter(description = "数据标签")
        @RequestHeader(required = false, value = Constants.HEADER_DATA_TAG_KEY)
        List<String> dataTag,
        @Parameter(description = "领域标签")
        @RequestHeader(required = false, value = Constants.HEADER_DOMAIN_LABEL_KEY)
        String labelJson,
        @RequestBody KnowledgeFileParseTaskDOParams params
    );

	@PostMapping("/conditions/page")
	@JsonView(ShowView.class)
	@Operation(summary = "通用条件分页查询")
	public ResultDTO<Page<KnowledgeFileParseTaskDO>> conditionsPage(
		@Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
		@RequestBody KnowledgeFileParseTaskDOParams params,
		Pageable pageable
	);

	@PostMapping("/conditions/page")
    @JsonView(ShowView.class)
    @Operation(summary = "通用条件分页查询")
    public ResultDTO<Page<KnowledgeFileParseTaskDO>> conditionsPage(
        @Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
        @Parameter(description = "数据标签")
        @RequestHeader(required = false, value = Constants.HEADER_DATA_TAG_KEY)
        List<String> dataTag,
        @Parameter(description = "领域标签")
        @RequestHeader(required = false, value = Constants.HEADER_DOMAIN_LABEL_KEY)
        String labelJson,
        @RequestBody KnowledgeFileParseTaskDOParams params,
        Pageable pageable
    );

    @PostMapping("/conditions/page")
  @JsonView(ShowView.class)
  @Operation(summary = "通用条件分页查询")
  public ResultDTO<Page<KnowledgeFileParseTaskDO>> conditionsPage(
      @Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
      @Parameter(description = "领域标签")
      @RequestHeader(required = false, value = Constants.HEADER_DOMAIN_LABEL_KEY)
      String labelJson,
      @RequestBody KnowledgeFileParseTaskDOParams body,
      @SpringQueryMap Pageable pageable
  );

  @PostMapping("/conditions")
  @JsonView(ShowView.class)
  @Operation(summary = "通用条件查询")
  public ResultDTO<List<KnowledgeFileParseTaskDO>> conditions(
      @Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
      @RequestBody KnowledgeFileParseTaskDOParams body,
      @Parameter(description = "排序参数")
      @RequestParam(required = false, value = "orderBy")
      String sort
  );

  @PostMapping("/conditions")
    @JsonView(ShowView.class)
    @Operation(summary = "通用条件查询")
    public ResultDTO<List<KnowledgeFileParseTaskDO>> conditions(
        @Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
        @Parameter(description = "数据标签")
        @RequestHeader(required = false, value = Constants.HEADER_DATA_TAG_KEY)
        List<String> dataTag,
        @Parameter(description = "领域标签")
        @RequestHeader(required = false, value = Constants.HEADER_DOMAIN_LABEL_KEY)
        String labelJson,
        @RequestBody KnowledgeFileParseTaskDOParams body,
        @Parameter(description = "排序参数")
        @RequestParam(required = false, value = "orderBy")
        String sort
    );

	@GetMapping("/count/enum")
    @Operation(summary = "枚举字段数量统计")
    public ResultDTO<Map<Object, Object>> count(
        @Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
        @SpringQueryMap KnowledgeFileParseTaskDOParams params,
        @Parameter(description = "统计的字段")@RequestParam(value = "fields") List<String> fields,
  @Parameter(description = "自定义mongo查询json")
        @RequestParam(required = false, value = "queryJson") String queryJson
   	);

    @GetMapping("/count/page")
    @Operation(summary = "按照字段分组进行统计数据")
    public ResultDTO<Page<Map>> countByFieldsPage(
        @Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
        @SpringQueryMap KnowledgeFileParseTaskDOParams params,
        @Parameter(description = "统计的字段")
        @RequestParam(required = true, value = "countField") String countField,
        @Parameter(description = "分组字段")
        @RequestParam(required = true, value = "groupField") String groupField,
        @Parameter(description = "筛选条件")
        @RequestParam(required = false, value = "filter") String filter,
        Pageable pageable
    );

	@PostMapping("/copy")
	@JsonView(ShowView.class)
	@Operation(summary = "数据批量复制接口")
	public ResultDTO copyAndModify(
		@Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
		@SpringQueryMap KnowledgeFileParseTaskDOParams params,
		@Parameter(description = "请求用户账号", example = "admin") @RequestHeader(required = true, value = Constants.HEADER_ACCOUNT_KEY) @NotBlank  String userAccount,
    @Parameter(description = "忽略不需要复制的属性名称", example = "id,createTime,updateTime") @RequestParam(required = false, value = "ignoreProperties") List<String> ignoreProperties,
		@RequestBody() @JsonView(AbstractBaseBO.EditView.class) KnowledgeFileParseTaskDO modify
    );
    @GetMapping("/distinct/knowledgeBaseID")
    @JsonView(ShowView.class)
    @Operation(summary = "查询knowledgeBaseID字段的不同取值列表")
    public ResultDTO<List<String>> distinctknowledgeBaseID(
        @Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
		@SpringQueryMap KnowledgeFileParseTaskDOParams params
    );

    
    @GetMapping("/distinct/knowledgeFileID")
    @JsonView(ShowView.class)
    @Operation(summary = "查询knowledgeFileID字段的不同取值列表")
    public ResultDTO<List<String>> distinctknowledgeFileID(
        @Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
		@SpringQueryMap KnowledgeFileParseTaskDOParams params
    );

    
    @GetMapping("/distinct/kbEmbdConfigID")
    @JsonView(ShowView.class)
    @Operation(summary = "查询kbEmbdConfigID字段的不同取值列表")
    public ResultDTO<List<String>> distinctkbEmbdConfigID(
        @Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
		@SpringQueryMap KnowledgeFileParseTaskDOParams params
    );

    
    @GetMapping("/distinct/fileName")
    @JsonView(ShowView.class)
    @Operation(summary = "查询fileName字段的不同取值列表")
    public ResultDTO<List<String>> distinctfileName(
        @Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
		@SpringQueryMap KnowledgeFileParseTaskDOParams params
    );

    @GetMapping("/distinct/status")
    @JsonView(ShowView.class)
    @Operation(summary = "查询status字段的不同取值列表")
    public ResultDTO<List<KbFileParseStatusEnum>> distinctstatus(
        @Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
		@SpringQueryMap KnowledgeFileParseTaskDOParams params
    );

    @GetMapping("/distinct/errorHandler")
    @JsonView(ShowView.class)
    @Operation(summary = "查询errorHandler字段的不同取值列表")
    public ResultDTO<List<KnowledgeErrorHandlerEnum>> distincterrorHandler(
        @Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
		@SpringQueryMap KnowledgeFileParseTaskDOParams params
    );

    @GetMapping("/distinct/maxRetryCount")
    @JsonView(ShowView.class)
    @Operation(summary = "查询maxRetryCount字段的不同取值列表")
    public ResultDTO<List<Integer>> distinctmaxRetryCount(
        @Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
		@SpringQueryMap KnowledgeFileParseTaskDOParams params
    );

    @GetMapping("/distinct/currentRetryCount")
    @JsonView(ShowView.class)
    @Operation(summary = "查询currentRetryCount字段的不同取值列表")
    public ResultDTO<List<Integer>> distinctcurrentRetryCount(
        @Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
		@SpringQueryMap KnowledgeFileParseTaskDOParams params
    );

    @GetMapping("/distinct/errorMessage")
    @JsonView(ShowView.class)
    @Operation(summary = "查询errorMessage字段的不同取值列表")
    public ResultDTO<List<String>> distincterrorMessage(
        @Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
		@SpringQueryMap KnowledgeFileParseTaskDOParams params
    );

    @GetMapping("/distinct/nextRetryTime")
    @JsonView(ShowView.class)
    @Operation(summary = "查询nextRetryTime字段的不同取值列表")
    public ResultDTO<List<Long>> distinctnextRetryTime(
        @Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
		@SpringQueryMap KnowledgeFileParseTaskDOParams params
    );

    @GetMapping("/distinct/groupIDs")
    @JsonView(ShowView.class)
    @Operation(summary = "查询groupIDs字段的不同取值列表")
    public ResultDTO<List<String>> distinctgroupIDs(
        @Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
		@SpringQueryMap KnowledgeFileParseTaskDOParams params
    );

    @GetMapping("/distinct/groupType")
    @JsonView(ShowView.class)
    @Operation(summary = "查询groupType字段的不同取值列表")
    public ResultDTO<List<GroupTypeEnum>> distinctgroupType(
        @Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
		@SpringQueryMap KnowledgeFileParseTaskDOParams params
    );
@GetMapping("/distinct/createBy")
    @JsonView(ShowView.class)
    @Operation(summary = "查询createBy字段的不同取值列表")
    public ResultDTO<List<String>> distinctCreateBy(
		@Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
		@SpringQueryMap KnowledgeFileParseTaskDOParams params
    );

    @PostMapping("/delete/condition")
    @JsonView(ShowView.class)
    @Operation(summary = "按条件删除-notifyParams")
    public ResultDTO deleteByCondition(
        @Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
        @SpringQueryMap NotifyParams notifyParams,
        @RequestBody KnowledgeFileParseTaskDOParams body
    );

    @PostMapping("/delete/condition")
    @JsonView(ShowView.class)
    @Operation(summary = "按条件删除")
    public ResultDTO deleteByCondition(
        @Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
        @RequestBody KnowledgeFileParseTaskDOParams body
    );

    @PostMapping("/delete/condition")
    @JsonView(ShowView.class)
    @Operation(summary = "按条件删除")
    public ResultDTO deleteByCondition(
        @Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
        @Parameter(description = "数据标签")
        @RequestHeader(required = false, value = Constants.HEADER_DATA_TAG_KEY)
        List<String> dataTag,
        @Parameter(description = "领域标签")
        @RequestHeader(required = false, value = Constants.HEADER_DOMAIN_LABEL_KEY)
        String labelJson,
        @RequestBody KnowledgeFileParseTaskDOParams body
    );

    @PostMapping("/update/condition")
    @JsonView(ShowView.class)
    @Operation(summary = "按条件修改")
    public ResultDTO updateByCondition(
        @Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
        @Parameter(description = "请求用户账号", example = "admin") @RequestHeader(required = true, value =
            Constants.HEADER_ACCOUNT_KEY) @NotBlank String userAccount,
        @Parameter(description = "是否不发送通知，true：不发送，false：发送", example = "false") @RequestParam(name =
            "notNotify", required = false) boolean notNotify,
        @Parameter(description = "是否发送变更通知给自己，true: 不发送，false: 发送", example = "false") @RequestParam(name =
            "notNotifyToSelf", required = false) boolean notNotifyToSelf,
        @Parameter(description = "需要删除的字段", example = "['field1', 'field2']") @RequestParam(name = "unsetFields",
            required = false) List<String> unsetFields,
        @RequestBody KnowledgeFileParseTaskDOUpdate conditionUpdate
    );
    

    @PostMapping("/update/condition")
    @JsonView(ShowView.class)
    @Operation(summary = "按条件修改")
    public ResultDTO updateByCondition(
        @Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
        @Parameter(description = "数据标签")
        @RequestHeader(required = false, value = Constants.HEADER_DATA_TAG_KEY)
        List<String> dataTag,
        @Parameter(description = "领域标签")
        @RequestHeader(required = false, value = Constants.HEADER_DOMAIN_LABEL_KEY)
        String labelJson,
        @Parameter(description = "请求用户账号", example = "admin") @RequestHeader(required = true, value =
            Constants.HEADER_ACCOUNT_KEY) @NotBlank String userAccount,
        @Parameter(description = "是否不发送通知，true：不发送，false：发送", example = "false") @RequestParam(name =
            "notNotify", required = false) boolean notNotify,
        @Parameter(description = "是否发送变更通知给自己，true: 不发送，false: 发送", example = "false") @RequestParam(name =
            "notNotifyToSelf", required = false) boolean notNotifyToSelf,
        @Parameter(description = "需要删除的字段", example = "['field1', 'field2']") @RequestParam(name = "unsetFields",
            required = false) List<String> unsetFields,
        @RequestBody KnowledgeFileParseTaskDOUpdate conditionUpdate
    );

    @PostMapping("/update/condition")
    @JsonView(ShowView.class)
    @Operation(summary = "按条件修改-notifyParams")
    public ResultDTO updateByCondition(
        @Parameter(description = "租户ID", example = "tenant-A")
		@RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
		String tenantID,
		@Parameter(description = "命名空间", example = "com.polarizon")
		@RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
		String namespace,
        @Parameter(description = "数据标签")
        @RequestHeader(required = false, value = Constants.HEADER_DATA_TAG_KEY)
        List<String> dataTag,
        @Parameter(description = "领域标签")
        @RequestHeader(required = false, value = Constants.HEADER_DOMAIN_LABEL_KEY)
        String labelJson,
        @Parameter(description = "请求用户账号", example = "admin") @RequestHeader(required = true, value =
            Constants.HEADER_ACCOUNT_KEY) @NotBlank String userAccount,
        @SpringQueryMap NotifyParams notifyParams,@Parameter(description = "需要删除的字段", example = "['field1', 'field2']") @RequestParam(name = "unsetFields",
            required = false) List<String> unsetFields,@RequestBody KnowledgeFileParseTaskDOUpdate conditionUpdate);

}
