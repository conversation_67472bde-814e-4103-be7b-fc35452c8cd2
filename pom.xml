<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.0.9</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>

    <name>kb-manage-polarinsight</name>
    <description>polarinsight-v1: kb-manage-polarinsight</description>
    <organization>
        <name>天海宸光 Polarizon</name>
        <url>https://www.polarizon.com/</url>
    </organization>

    <groupId>com.polarizon.rag</groupId>
    <artifactId>kb-manage-polarinsight-ms</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>

    <properties>
        <!-- 全局版本控制，如果要修改版本号，修改此处即可-->
        <revision>1.0.0-beta.4-alpha</revision>
        <dev.namespace>polarinsight-v1</dev.namespace>
    </properties>

    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
                <version>2.7</version>
            </plugin>
            <!-- 添加flatten-maven-plugin插件 -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>1.5.0</version>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
                <inherited>true</inherited>
                <configuration>
                    <!-- 避免IDE将 .flattened-pom.xml 自动识别为功能模块 -->
                    <updatePomFile>true</updatePomFile>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <modules>
        <module>autocode/api</module>
        <module>plugin</module>
        <module>autocode/biz</module>
    </modules>

    <!-- 分发配置，必须与 settings.xml 的 id 一致 -->
    <distributionManagement>
        <repository>
            <id>nexus-releases</id>
            <url>http://192.168.20.201:8081/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>nexus-snapshots</id>
            <url>http://192.168.20.201:8081/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

</project>
