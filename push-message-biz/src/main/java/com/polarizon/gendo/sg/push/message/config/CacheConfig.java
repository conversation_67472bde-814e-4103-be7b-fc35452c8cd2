package com.polarizon.gendo.sg.push.message.config;

import com.github.benmanes.caffeine.cache.Caffeine;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.CacheManager;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.concurrent.TimeUnit;

@Configuration
public class CacheConfig {
    /**
     * 访问token缓存过期时间
     */
    @Value("${com.polarizon.access-token-expires:6000}")
    private Integer accessTokenExpires;

    /**
     * 配置缓存管理器
     *
     * @return
     */
    @Bean(name = "caffeineCacheManger")

    public CacheManager caffeineCacheManger() {
        CaffeineCacheManager caffeineCacheManger = new CaffeineCacheManager();
        caffeineCacheManger.setCaffeine(Caffeine.newBuilder().
                //缓存过期时间
                        expireAfterWrite(accessTokenExpires, TimeUnit.SECONDS).
                //缓存初始容量
                        initialCapacity(100));
        caffeineCacheManger.
                setCacheNames(Arrays.asList("default", "accessTokenCache"));
        return caffeineCacheManger;
    }
}
