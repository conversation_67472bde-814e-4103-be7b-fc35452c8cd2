package com.polarizon.rag.plugin.common.service.task.fileAdd;

import cn.hutool.extra.spring.SpringUtil;
import com.polarizon.gendo.common.dto.ResultDTO;
import com.polarizon.rag.plugin.common.client.EmbeddingClient;
import com.polarizon.rag.plugin.common.dto.DocumentEmbeddingResp;
import com.polarizon.rag.plugin.common.service.task.AbstractTask;
import com.polarizon.rag.plugin.common.util.ResultDTOUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class DocumentEmbeddingTask extends AbstractTask<FileAddContext> {

    @Autowired
    private EmbeddingClient embeddingClient;

    @Override
    protected void handler(FileAddContext data) {
        try {
            ResultDTO<DocumentEmbeddingResp> documentEmbeddingResultDTO = embeddingClient.documentEmbedding(data.toParams());
            DocumentEmbeddingResp documentEmbeddingResData = ResultDTOUtil.getDataNonNull(documentEmbeddingResultDTO, "documentEmbedding error");
            log.debug("documentEmbeddingResData：{}", documentEmbeddingResData);
            data.setChunks(null == documentEmbeddingResData ? null : documentEmbeddingResData.getChunks());
            data.setCaptures(null == documentEmbeddingResData ? null : documentEmbeddingResData.getCaptures());
            data.setSuccess(true);
            // TODO 超时处理
        } catch (Exception e) {
            data.setSuccess(false);
            data.setRemark("FileParseTask error: " + e.getMessage());
            log.error("FileParseTask error:{}", e.getMessage());
        }
    }

    @Override
    public int getTaskCore() {
        return Integer.parseInt(SpringUtil.getApplicationContext().getEnvironment().getProperty("task.document_parse_core_pool_size", "10"));
    }

}
