package com.polarizon.rag.plugin.common.service.task.example;

import com.polarizon.rag.plugin.common.service.task.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 知识库文件解析使用示例
 */
@Slf4j
@Component
public class KbFileParseExample {

    @Autowired
    private KbFileParseManager kbFileParseManager;

    @Autowired
    private KbFileParseService kbFileParseService;

    /**
     * 示例：为知识库的多个配置创建文件解析任务
     */
    public void exampleCreateParseTasks() {
        String kbID = "kb_12345";
        String fileID = "file_67890";
        String fileName = "example.pdf";
        String userAccount = "<EMAIL>";
        
        // 一个知识库可能有多个配置
        List<String> kbEmbdConfigIDs = Arrays.asList(
                "config_001",  // 配置1：使用OpenAI模型
                "config_002",  // 配置2：使用本地模型
                "config_003"   // 配置3：使用其他模型
        );

        // 为每个配置创建解析任务
        List<String> taskIds = kbFileParseManager.createParseTasksForMultipleConfigs(
                kbID, kbEmbdConfigIDs, fileID, fileName, userAccount
        );

        log.info("成功创建 {} 个解析任务: {}", taskIds.size(), taskIds);
    }

    /**
     * 示例：监控解析进度
     */
    public void exampleMonitorProgress() {
        String kbID = "kb_12345";
        
        // 获取知识库所有配置的解析状态概览
        Map<String, KbFileParseManager.ConfigParseOverview> overview = 
                kbFileParseManager.getConfigParseOverview(kbID);
        
        log.info("知识库 {} 的解析状态概览:", kbID);
        for (Map.Entry<String, KbFileParseManager.ConfigParseOverview> entry : overview.entrySet()) {
            String configID = entry.getKey();
            KbFileParseManager.ConfigParseOverview configOverview = entry.getValue();
            
            log.info("配置 {}: 总文件={}, 已完成={}, 失败={}, 等待中={}, 运行中={}, 完成率={:.1f}%",
                    configID,
                    configOverview.getTotalFiles(),
                    configOverview.getCompletedFiles(),
                    configOverview.getFailedFiles(),
                    configOverview.getPendingFiles(),
                    configOverview.getRunningFiles(),
                    configOverview.getCompletionRate()
            );
        }

        // 获取整体解析进度统计
        KbFileParseManager.ParseProgressStats stats = kbFileParseManager.getParseProgressStats(kbID);
        log.info("整体进度: 总任务={}, 已完成={}, 失败={}, 成功率={:.1f}%",
                stats.getTotalTasks(),
                stats.getCompletedTasks(),
                stats.getFailedTasks(),
                stats.getSuccessRate()
        );
    }

    /**
     * 示例：处理特定配置的解析任务
     */
    public void exampleProcessSpecificConfig() {
        String kbID = "kb_12345";
        String kbEmbdConfigID = "config_001";
        
        // 获取特定配置的所有任务
        List<KbFileParseTask> configTasks = kbFileParseService.getTasksByConfig(kbID, kbEmbdConfigID);
        
        log.info("配置 {} 有 {} 个任务", kbEmbdConfigID, configTasks.size());
        
        for (KbFileParseTask task : configTasks) {
            log.info("任务 {}: 文件={}, 状态={}, 进度={}%, 步骤={}",
                    task.getId(),
                    task.getFileName(),
                    task.getStatus(),
                    task.getProgress(),
                    task.getCurrentStep()
            );
            
            // 根据任务状态进行相应处理
            switch (task.getStatus()) {
                case PENDING:
                    // 开始解析
                    kbFileParseService.startParse(task.getId());
                    break;
                    
                case PARSING:
                    // 更新进度
                    kbFileParseService.updateProgress(task.getId(), 50, "EMBEDDING");
                    break;
                    
                case EMBEDDING:
                    // 更新进度
                    kbFileParseService.updateProgress(task.getId(), 80, "FINALIZING");
                    break;
                    
                case FAILED:
                    // 检查是否需要重试
                    if (task.getCurrentRetryCount() < task.getMaxRetryCount()) {
                        log.info("重试失败的任务: {}", task.getId());
                        kbFileParseService.retryTask(task.getId());
                    }
                    break;
                    
                case COMPLETED:
                    log.info("任务已完成: {}", task.getId());
                    break;
            }
        }
    }

    /**
     * 示例：错误处理和重试
     */
    public void exampleErrorHandling() {
        String kbID = "kb_12345";
        
        // 检查是否有未完成的任务
        boolean hasUnfinished = kbFileParseManager.hasUnfinishedTasks(kbID);
        if (hasUnfinished) {
            log.info("知识库 {} 有未完成的解析任务", kbID);
            
            // 获取所有未完成的任务
            List<KbFileParseTask> unfinishedTasks = kbFileParseService.getUnfinishedTasks();
            log.info("未完成任务数量: {}", unfinishedTasks.size());
            
            // 重试所有失败的任务
            int retryCount = kbFileParseManager.retryAllFailedTasks(kbID);
            log.info("重试了 {} 个失败任务", retryCount);
        }
    }

    /**
     * 示例：批量处理多个文件
     */
    public void exampleBatchProcessFiles() {
        String kbID = "kb_12345";
        String userAccount = "<EMAIL>";
        
        // 假设有多个文件需要处理
        List<FileInfo> files = Arrays.asList(
                new FileInfo("file_001", "document1.pdf"),
                new FileInfo("file_002", "document2.docx"),
                new FileInfo("file_003", "presentation.pptx")
        );
        
        // 知识库的多个配置
        List<String> kbEmbdConfigIDs = Arrays.asList("config_001", "config_002");
        
        for (FileInfo file : files) {
            try {
                // 为每个文件在每个配置下创建解析任务
                List<String> taskIds = kbFileParseManager.createParseTasksForMultipleConfigs(
                        kbID, kbEmbdConfigIDs, file.getFileID(), file.getFileName(), userAccount
                );
                
                log.info("文件 {} 创建了 {} 个解析任务", file.getFileName(), taskIds.size());
                
                // 开始解析第一个任务
                if (!taskIds.isEmpty()) {
                    kbFileParseService.startParse(taskIds.get(0));
                }
                
            } catch (Exception e) {
                log.error("处理文件 {} 失败", file.getFileName(), e);
            }
        }
    }

    /**
     * 文件信息
     */
    public static class FileInfo {
        private String fileID;
        private String fileName;
        
        public FileInfo(String fileID, String fileName) {
            this.fileID = fileID;
            this.fileName = fileName;
        }
        
        public String getFileID() { return fileID; }
        public String getFileName() { return fileName; }
    }
}
