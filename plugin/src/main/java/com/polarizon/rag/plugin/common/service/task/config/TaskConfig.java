package com.polarizon.rag.plugin.common.service.task.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 任务配置类
 */
@Configuration
@EnableScheduling
public class TaskConfig {

    /**
     * 配置ObjectMapper用于任务序列化
     */
    @Bean
    public ObjectMapper taskObjectMapper() {
        return new ObjectMapper();
    }
}
