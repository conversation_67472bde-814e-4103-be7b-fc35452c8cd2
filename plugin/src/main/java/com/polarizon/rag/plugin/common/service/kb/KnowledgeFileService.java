package com.polarizon.rag.plugin.common.service.kb;

import cn.hutool.core.collection.CollectionUtil;
import com.polarizon.rag.ModelInfoDO;
import com.polarizon.rag.ModelTypeEnum;
import com.polarizon.rag.kb.*;
import com.polarizon.rag.kb.enums.EmbdStatusEnum;
import com.polarizon.rag.kb.feign.KnowledgeBaseDOFeign;
import com.polarizon.rag.kb.feign.KnowledgeFileEmbdOverviewDOFeign;
import com.polarizon.rag.kb.params.KnowledgeFileEmbdOverviewDOParams;
import com.polarizon.rag.plugin.common.service.TaskHandler;
import com.polarizon.rag.plugin.common.service.model.ModelService;
import com.polarizon.rag.plugin.common.service.task.fileAdd.FileAddContext;
import com.polarizon.rag.plugin.common.service.task.fileDel.FileDelContext;
import com.polarizon.rag.plugin.common.util.GetBeanHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class KnowledgeFileService {

    @Autowired
    private KnowledgeBaseDOFeign knowledgeBaseDOFeign;

    @Autowired
    private KnowledgeFileEmbdOverviewDOFeign knowledgeFileEmbdOverviewDOFeign;

    @Autowired
    private ModelService modelService;


    /**
     * 知识库文件解析
     *
     * @param kbFile       知识库文件
     * @param kbEmbdConfig 向量化配置
     */
    public void kbFileParse(KnowledgeFileDO kbFile, KnowledgeBaseEmbdConfigDO kbEmbdConfig) {
        // 获取向量化配置
        VectorConfig vectorConfig = kbEmbdConfig.getVectorConfig();
        if (Objects.isNull(vectorConfig)) {
            return;
        }

        // 获取模型配置
        KnowledgeModelConfig ocrModelConfig = vectorConfig.getOcrModelConfig();
        KnowledgeModelConfig embeddingModelConfig = vectorConfig.getEmbeddingModelConfig();
        if (Objects.isNull(ocrModelConfig) || Objects.isNull(embeddingModelConfig)) {
            return;
        }

        // 获取模型信息
        ModelInfoDO ocrModelInfo = modelService.getModel(ocrModelConfig, ModelTypeEnum.OCR);
        ModelInfoDO embeddingModelInfo = modelService.getModel(embeddingModelConfig, ModelTypeEnum.EMBEDDING);

        // 获取chunk设置
        Integer chunkSize = vectorConfig.getChunkSetting().getChunkSize();
        Integer chunkOverlap = vectorConfig.getChunkSetting().getChunkOverlap();

        // 删除原有文件向量化概览
        knowledgeFileEmbdOverviewDOFeign.deleteByCondition(null, null, KnowledgeFileEmbdOverviewDOParams.builder().knowledgeFileID(List.of(kbFile.getId())).kbEmbdConfigID(List.of(kbEmbdConfig.getId())).build());

        // 添加知识文件向量化概览
        KnowledgeFileEmbdOverviewDO kbFileEmbdOverview = KnowledgeFileEmbdOverviewDO.builder()
                .knowledgeBaseID(kbFile.getKnowledgeBaseID()).knowledgeFileID(kbFile.getId()).kbEmbdConfigID(kbEmbdConfig.getId())
                .status(EmbdStatusEnum.INIT).build();
        String kbFileEmbdOverviewID = knowledgeFileEmbdOverviewDOFeign.add(null, null, "root", kbFileEmbdOverview).getData().getId();

        // 向量化模型入队
        TaskHandler.getFileAddTask().inputTask(FileAddContext.of(kbFile, kbEmbdConfig.getId(), kbFileEmbdOverviewID, chunkSize, chunkOverlap, ocrModelInfo, embeddingModelInfo));
    }


    /**
     * 删除知识库文件
     *
     * @param task 知识库文件列表
     */
    public void deleteKnowledgeFile(List<KnowledgeFileDO> task) {
        if (CollectionUtil.isEmpty(task)) {
            return;
        }

        // 删除S3文件
        for (KnowledgeFileDO file : task) {
            if (Objects.nonNull(file.getFileS3()) && StringUtils.isNotEmpty(file.getFileS3().getBucketName()) && StringUtils.isNotEmpty(file.getFileS3().getKey())) {
                GetBeanHelper.getAwsUtils().deleteFromS3(file.getFileS3().getBucketName(), file.getFileS3().getKey());
            }
        }

        // 获取知识库ID
        List<String> knowledgeBaseIDs = task.stream().map(KnowledgeFileDO::getKnowledgeBaseID).filter(StringUtils::isNotEmpty).toList();
        if (CollectionUtil.isEmpty(knowledgeBaseIDs)) {
            return;
        }

        // 获取知识库
        List<KnowledgeBaseDO> knowledgeBaseDOs = knowledgeBaseDOFeign.listAll(null, null, knowledgeBaseIDs).getData();
        if (CollectionUtil.isEmpty(knowledgeBaseDOs)) {
            return;
        }

        // 删除知识库文件
        for (KnowledgeFileDO file : task) {
            if (Objects.nonNull(file.getFileS3())) {
                TaskHandler.getFileDelTask().inputTask(FileDelContext.of(file));
            }
        }
    }

}
