package com.polarizon.rag.plugin.common.service.task.fileAdd;

import cn.hutool.core.collection.CollectionUtil;
import com.polarizon.gendo.common.dto.ResultDTO;
import com.polarizon.gendo.common.dto.ValidatedList;
import com.polarizon.rag.kb.KnowledgeFileCaptureDO;
import com.polarizon.rag.kb.KnowledgeFileChunkDO;
import com.polarizon.rag.kb.KnowledgeFileEmbdOverviewDO;
import com.polarizon.rag.kb.enums.EmbdStatusEnum;
import com.polarizon.rag.kb.feign.KnowledgeFileCaptureDOFeign;
import com.polarizon.rag.kb.feign.KnowledgeFileChunkDOFeign;
import com.polarizon.rag.kb.feign.KnowledgeFileEmbdOverviewDOFeign;
import com.polarizon.rag.kb.params.KnowledgeFileCaptureDOParams;
import com.polarizon.rag.kb.params.KnowledgeFileChunkDOParams;
import com.polarizon.rag.plugin.common.dto.DocumentEmbeddingResp;
import com.polarizon.rag.plugin.common.service.task.AbstractTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;


@Slf4j
@Component
public class ParseInputDBTask extends AbstractTask<FileAddContext> {

    @Autowired
    private KnowledgeFileCaptureDOFeign knowledgeFileCaptureDOFeign;

    @Autowired
    private KnowledgeFileChunkDOFeign knowledgeFileChunkDOFeign;

    @Autowired
    private KnowledgeFileEmbdOverviewDOFeign knowledgeFileEmbdOverviewDOFeign;


    @Override
    protected void handler(FileAddContext data) {
        Map<Integer, KnowledgeFileCaptureDO> captureMap = new ConcurrentHashMap<>();

        // 落库保存截图
        if (CollectionUtil.isNotEmpty(data.getUploadCaptures())) {
            List<KnowledgeFileCaptureDO> saveCaptureList = data.getUploadCaptures().stream()
                    .map(s -> toCaptureDO(data.getKbID(), data.getFileID(), s))
                    .toList();

            // 入库之前删除旧数据，确保数据不重复
            knowledgeFileCaptureDOFeign.deleteByCondition(null, null,
                    KnowledgeFileCaptureDOParams.builder().knowledgeFileID(List.of(data.getFileID())).build());

            ResultDTO<Iterable<KnowledgeFileCaptureDO>> addCaptureResult = knowledgeFileCaptureDOFeign
                    .addAll(null, null, "root", true, new ValidatedList<>(saveCaptureList));

            addCaptureResult.getData().forEach(s -> captureMap.put(s.getPageNumber(), s));
        }

        // 落库保存chunk
        if (CollectionUtil.isNotEmpty(data.getChunks())) {
            List<KnowledgeFileChunkDO> saveList = data.getChunks().stream()
                    .map(s -> toChunkDO(data, captureMap, s))
                    .toList();

            // 入库之前删除旧数据，确保数据不重复
            knowledgeFileChunkDOFeign.deleteByCondition(null, null,
                    KnowledgeFileChunkDOParams.builder().knowledgeFileID(List.of(data.getFileID())).build());

            knowledgeFileChunkDOFeign.addAll(null, null, "root",
                    true, new ValidatedList<>(saveList));
        }

        // 落库保存EmbdConfig
        KnowledgeFileEmbdOverviewDO embdConfigDO = toEmbdConfigDO(data);
        knowledgeFileEmbdOverviewDOFeign.update(null, null, "root", false, true, embdConfigDO);
    }

    private KnowledgeFileChunkDO toChunkDO(FileAddContext data, Map<Integer, KnowledgeFileCaptureDO> captureMap, DocumentEmbeddingResp.Chunk s) {
        KnowledgeFileChunkDO chunk = new KnowledgeFileChunkDO();
        chunk.setKnowledgeBaseID(data.getKbID());
        chunk.setKnowledgeFileID(data.getFileID());
        chunk.setKbEmbdConfigID(data.getKbEmbdConfigID());
        chunk.setChapterID(s.getChapterID());
        chunk.setChapterName(s.getChapterName());
        chunk.setParentChapterName(s.getParentChapterName());
        chunk.setParentChapterID(s.getParentChapterID());
        chunk.setContent(s.getContent());
        Integer pageNumber = s.getPageNumber();
        chunk.setPageNumber(pageNumber);
        chunk.setChunkID(s.getChunkID());
        if (pageNumber != null) {
            Optional.ofNullable(captureMap.get(pageNumber)).ifPresent(t -> {
                chunk.setCapture(t.getCapture());
                chunk.setCaptureID(t.getId());
            });
        }

        return chunk;
    }

    private KnowledgeFileEmbdOverviewDO toEmbdConfigDO(FileAddContext data) {
        KnowledgeFileEmbdOverviewDO embdOverview = new KnowledgeFileEmbdOverviewDO();
        embdOverview.setId(data.getKbFileEmbdOverviewID());
        embdOverview.setKnowledgeBaseID(data.getKbID());
        embdOverview.setKnowledgeFileID(data.getFileID());
        embdOverview.setKbEmbdConfigID(data.getKbEmbdConfigID());
        embdOverview.setTotalChunks(CollectionUtil.size(data.getChunks()));
        embdOverview.setTotalCaptures(CollectionUtil.size(data.getCaptures()));

        if (data.isSuccess()) {
            embdOverview.setStatus(EmbdStatusEnum.FINISH);
        } else {
            embdOverview.setStatus(EmbdStatusEnum.FAILED);
            embdOverview.setFailureReason(data.getRemark());
        }

        return embdOverview;
    }

    private KnowledgeFileCaptureDO toCaptureDO(String kbID, String fileID, FileAddContext.CaptureContext s) {
        return KnowledgeFileCaptureDO.builder()
                .capture(s.getFile())
                .pageNumber(s.getPageNumber())
                .knowledgeFileID(fileID)
                .knowledgeBaseID(kbID)
                .build();
    }
}
