package com.polarizon.rag.plugin.common.service.task.fileAdd;

import com.polarizon.common.S3File;
import com.polarizon.rag.ModelInfoDO;
import com.polarizon.rag.kb.KnowledgeFileDO;
import com.polarizon.rag.plugin.common.dto.DocumentEmbeddingResp;
import com.polarizon.rag.plugin.common.dto.ParseFileParam;
import com.polarizon.rag.plugin.common.service.task.TaskContext;
import com.polarizon.rag.plugin.common.util.GetBeanHelper;
import lombok.Builder;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 文件解析上下文
 */
@Data
@Builder
public class FileAddContext extends TaskContext {
    // 第一步 任务下发的时候有的数据
    private String s3BucketName;
    private String s3Key;
    private String kbID;
    private String kbEmbdConfigID;
    private String ocrModel;
    private String ocrApiKey;
    private String ocrUrl;
    private Integer chunkSize;
    private Integer chunkOverlap;
    private String embeddingModel;
    private String embeddingApiKey;
    private String embeddingUrl;
    private String fileID;
    private String fileName;
    private String userAccount;
    private String kbFileEmbdOverviewID;

    // 第二步 存储解析到的数据
    private List<DocumentEmbeddingResp.Chunk> chunks;
    private List<DocumentEmbeddingResp.Capture> captures;

    // 第三步 上传图片
    private List<CaptureContext> uploadCaptures;


    @Data
    @Builder
    public static class CaptureContext {
        private S3File file;
        private Integer pageNumber;
    }

    // 结果更新
    @Builder.Default
    private boolean success = false;
    private String remark;

    public ParseFileParam toParams() {
        Date date = new Date(System.currentTimeMillis() + 1000 * 60 * 60 * 24 * 1);
        return ParseFileParam.builder()
                .kbID(kbID)
                .kbEmbdConfigID(kbEmbdConfigID)
                .fileName(fileName)
                .fileID(fileID)
                .fileURI(GetBeanHelper.getAwsUtils().generatePresignedUrl(s3BucketName,s3Key, date).toString())
                .ocrUrl(ocrUrl)
                .ocrModel(ocrModel)
                .ocrApiKey(ocrApiKey)
                .chunkSize(chunkSize)
                .chunkOverlap(chunkOverlap)
                .embeddingUrl(embeddingUrl)
                .embeddingModel(embeddingModel)
                .embeddingApiKey(embeddingApiKey)
                .includeCapture(true)
                .aggregated("paragraph")
                .build();
    }

    public static FileAddContext of(KnowledgeFileDO addFile, String kbEmbdConfigID, String kbFileEmbdOverviewID, Integer chunkSize, Integer chunkOverlap, ModelInfoDO ocrModelInfo, ModelInfoDO embeddingModelInfo){
        S3File fileS3 = addFile.getFileS3();
        return FileAddContext.builder()
                .s3BucketName(fileS3 == null? null : fileS3.getBucketName())
                .s3Key(fileS3 == null ? null : fileS3.getKey())
                .kbID(addFile.getKnowledgeBaseID())
                .kbEmbdConfigID(kbEmbdConfigID)
                .kbFileEmbdOverviewID(kbFileEmbdOverviewID)
                .fileName(addFile.getName())
                .fileID(addFile.getId())
                .ocrApiKey(ocrModelInfo.getApiKey())
                .ocrModel(ocrModelInfo.getName())
                .ocrUrl(ocrModelInfo.getEndpointUrl())
                .chunkSize(chunkSize)
                .chunkOverlap(chunkOverlap)
                .embeddingApiKey(embeddingModelInfo.getApiKey())
                .embeddingModel(embeddingModelInfo.getName())
                .embeddingUrl(embeddingModelInfo.getEndpointUrl())
                .userAccount(addFile.getCreateBy())
                .build();
    }

    public void error(String errorRemark){
        this.success = false;
        this.remark = errorRemark;
    }
}
